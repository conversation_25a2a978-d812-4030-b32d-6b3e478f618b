#!/usr/bin/env python3
"""
Interactive Element Selector API Service Launcher

This script launches the standalone Interactive Element Selector API service
for GretahAI ScriptWeaver. It can be run independently from the main Streamlit
application to provide isolated element selection functionality.

Usage:
    python start_selector_api.py [--host HOST] [--port PORT] [--debug]

© 2025 Cogniron All Rights Reserved.
"""

import argparse
import logging
import os
import sys
import signal
import subprocess
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'requests',
        'selenium'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.error("Please install them using: pip install " + " ".join(missing_packages))
        return False
    
    return True


def check_webdriver():
    """Check if WebDriver is properly configured"""
    try:
        from core.interactive_selector import setup_interactive_webdriver
        
        logger.info("Testing WebDriver setup...")
        driver = setup_interactive_webdriver()
        
        if driver:
            driver.quit()
            logger.info("✅ WebDriver setup successful")
            return True
        else:
            logger.error("❌ WebDriver setup failed")
            return False
    
    except Exception as e:
        logger.error(f"❌ WebDriver test failed: {e}")
        return False


def start_api_service(host: str = "127.0.0.1", port: int = 8502, debug: bool = False):
    """
    Start the Interactive Element Selector API service.
    
    Args:
        host: Host to bind the service to
        port: Port to bind the service to
        debug: Enable debug mode
    """
    try:
        # Set environment variables
        os.environ["SELECTOR_API_HOST"] = host
        os.environ["SELECTOR_API_PORT"] = str(port)
        
        if debug:
            os.environ["SELECTOR_API_DEBUG"] = "true"
            log_level = "debug"
        else:
            log_level = "info"
        
        logger.info(f"Starting Interactive Element Selector API service...")
        logger.info(f"Host: {host}")
        logger.info(f"Port: {port}")
        logger.info(f"Debug: {debug}")
        
        # Import and run the service
        import uvicorn
        from api.interactive_selector_service import app
        
        # Configure uvicorn
        config = uvicorn.Config(
            app=app,
            host=host,
            port=port,
            log_level=log_level,
            reload=debug,
            access_log=debug
        )
        
        server = uvicorn.Server(config)
        
        # Handle graceful shutdown
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal, stopping API service...")
            server.should_exit = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start the server
        logger.info(f"🚀 Interactive Element Selector API service starting on http://{host}:{port}")
        logger.info("Press Ctrl+C to stop the service")
        
        server.run()
        
    except KeyboardInterrupt:
        logger.info("Service stopped by user")
    except Exception as e:
        logger.error(f"Failed to start API service: {e}")
        sys.exit(1)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Start the Interactive Element Selector API service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python start_selector_api.py
    python start_selector_api.py --host 0.0.0.0 --port 8503
    python start_selector_api.py --debug
        """
    )
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind the service to (default: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8502,
        help="Port to bind the service to (default: 8502)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode with auto-reload"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check dependencies and exit"
    )
    
    parser.add_argument(
        "--check-webdriver",
        action="store_true",
        help="Test WebDriver setup and exit"
    )
    
    args = parser.parse_args()
    
    # Handle check commands
    if args.check_deps:
        if check_dependencies():
            logger.info("✅ All dependencies are installed")
            sys.exit(0)
        else:
            sys.exit(1)
    
    if args.check_webdriver:
        if check_webdriver():
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Validate arguments
    if not (1 <= args.port <= 65535):
        logger.error("Port must be between 1 and 65535")
        sys.exit(1)
    
    # Check dependencies before starting
    logger.info("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    # Check WebDriver setup
    logger.info("Checking WebDriver setup...")
    if not check_webdriver():
        logger.warning("WebDriver setup test failed, but continuing anyway...")
        logger.warning("Element selection may not work properly")
    
    # Start the service
    start_api_service(
        host=args.host,
        port=args.port,
        debug=args.debug
    )


if __name__ == "__main__":
    main()
