#!/usr/bin/env python3
"""
Debug script to test the complete Stage 10 workflow and gap analysis.
"""

import sys
import os
import json

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_stage10_workflow():
    """Test the Stage 10 workflow components."""
    print("=== Stage 10 Workflow Debug ===")
    
    try:
        # Test 1: Check state configuration
        print("\n1. Testing State Configuration:")
        from state_manager import StateManager
        state = StateManager()
        print(f"   Default website_url: {getattr(state, 'website_url', 'NOT_SET')}")
        
        # Set a test website URL
        state.website_url = "https://example.com/login"
        print(f"   Test website_url: {state.website_url}")
        print(f"   Interactive selector enabled: {state.website_url and state.website_url != 'https://example.com'}")
        
        # Test 2: Check gap analysis imports
        print("\n2. Testing Gap Analysis Imports:")
        try:
            from core.gap_analysis import (
                analyze_template_gaps,
                display_gap_filling_form
                # REMOVED: _render_locator_gap_input, _launch_interactive_selector_for_gap - deprecated functions
            )
            print("   ✅ All gap analysis functions imported successfully")
        except Exception as e:
            print(f"   ❌ Gap analysis import error: {e}")
            return
        
        # Test 3: Check interactive selector imports
        print("\n3. Testing Interactive Selector Imports:")
        try:
            from core.interactive_selector import select_element_interactively
            print("   ✅ Interactive selector imported successfully")
        except Exception as e:
            print(f"   ❌ Interactive selector import error: {e}")
        
        # Test 4: Create test gaps
        print("\n4. Testing Gap Creation:")
        test_gaps = [
            {
                'id': 'login_button',
                'type': 'locator',
                'description': 'CSS selector for the login button',
                'required': True,
                'suggested_values': ['#login', '.login-btn', 'button[type="submit"]'],
                'impact': 'high',
                'category': 'ui_element'
            },
            {
                'id': 'username_field',
                'type': 'locator', 
                'description': 'Locator for username input field',
                'required': True,
                'suggested_values': ['#username', 'input[name="username"]'],
                'impact': 'high',
                'category': 'ui_element'
            },
            {
                'id': 'test_data',
                'type': 'text',
                'description': 'Test username value',
                'required': True,
                'suggested_values': ['testuser', 'admin'],
                'impact': 'medium',
                'category': 'test_data'
            }
        ]
        
        locator_gaps = [gap for gap in test_gaps if gap.get('type') == 'locator']
        print(f"   Created {len(test_gaps)} total gaps")
        print(f"   Found {len(locator_gaps)} locator gaps")
        
        # Test 5: Test gap analysis logic
        print("\n5. Testing Gap Analysis Logic:")
        
        # Mock template script
        template_script = {
            'id': 'test_template',
            'name': 'Login Test Template',
            'script_content': 'driver.find_element(By.CSS_SELECTOR, "#login").click()',
            'metadata': {
                'test_case_objective': 'Test user login functionality'
            }
        }
        
        # Mock target test case
        target_test_case = {
            'Test Case ID': 'TC001',
            'Test Case Name': 'User Login Test',
            'Test Case Objective': 'Verify user can login successfully'
        }
        
        print(f"   Template script: {template_script['name']}")
        print(f"   Target test case: {target_test_case['Test Case Name']}")
        
        # Test 6: Check website URL validation
        print("\n6. Testing Website URL Validation:")
        test_urls = [
            "https://example.com",
            "https://example.com/login", 
            "http://localhost:3000",
            None,
            ""
        ]
        
        for url in test_urls:
            enabled = url and url != "https://example.com"
            print(f"   URL: {url} -> Interactive selector enabled: {enabled}")
        
        print("\n=== Debug Complete ===")
        print("\nNext Steps:")
        print("1. Run the main application and navigate to Stage 2")
        print("2. Set a valid website URL (not https://example.com)")
        print("3. Navigate to Stage 10 and select a template + test case")
        print("4. Check if gap analysis identifies locator gaps")
        print("5. Verify that interactive selector buttons appear")
        
    except Exception as e:
        print(f"Error in workflow test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stage10_workflow()
