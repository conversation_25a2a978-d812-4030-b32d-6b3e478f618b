#!/usr/bin/env python3
"""
GretahAI ScriptWeaver Launcher with Interactive Selector API

This script launches both the main Streamlit application and the standalone
Interactive Element Selector API service for optimal functionality.

Usage:
    python start_with_api.py [options]

© 2025 Cogniron All Rights Reserved.
"""

import argparse
import logging
import os
import signal
import subprocess
import sys
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global process tracking
processes = []


def cleanup_processes():
    """Clean up all spawned processes."""
    logger.info("Cleaning up processes...")
    
    for process in processes:
        if process.poll() is None:  # Process is still running
            try:
                process.terminate()
                process.wait(timeout=5)
                logger.info(f"Terminated process {process.pid}")
            except subprocess.TimeoutExpired:
                logger.warning(f"Force killing process {process.pid}")
                process.kill()
                process.wait()
            except Exception as e:
                logger.error(f"Error terminating process {process.pid}: {e}")


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    cleanup_processes()
    sys.exit(0)


def check_port_available(host, port):
    """Check if a port is available."""
    import socket
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            return result != 0  # Port is available if connection fails
    except Exception:
        return False


def wait_for_service(host, port, timeout=30):
    """Wait for a service to become available."""
    import socket
    
    logger.info(f"Waiting for service at {host}:{port}...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                if result == 0:  # Connection successful
                    logger.info(f"✅ Service at {host}:{port} is ready")
                    return True
        except Exception:
            pass
        
        time.sleep(1)
    
    logger.error(f"❌ Service at {host}:{port} did not become available within {timeout} seconds")
    return False


def start_api_service(host="127.0.0.1", port=8502, debug=False):
    """Start the Interactive Element Selector API service."""
    logger.info(f"Starting Interactive Element Selector API service on {host}:{port}")
    
    # Check if port is available
    if not check_port_available(host, port):
        logger.error(f"Port {port} is already in use")
        return None
    
    # Prepare command
    cmd = [
        sys.executable,
        "api/start_selector_api.py",
        "--host", host,
        "--port", str(port)
    ]
    
    if debug:
        cmd.append("--debug")
    
    try:
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        processes.append(process)
        logger.info(f"API service started with PID {process.pid}")
        
        # Wait for service to be ready
        if wait_for_service(host, port):
            return process
        else:
            logger.error("API service failed to start properly")
            process.terminate()
            return None
    
    except Exception as e:
        logger.error(f"Failed to start API service: {e}")
        return None


def start_streamlit_app(host="127.0.0.1", port=8501, debug=False):
    """Start the main Streamlit application."""
    logger.info(f"Starting Streamlit application on {host}:{port}")
    
    # Check if port is available
    if not check_port_available(host, port):
        logger.error(f"Port {port} is already in use")
        return None
    
    # Prepare command
    cmd = [
        sys.executable,
        "-m", "streamlit", "run",
        "app.py",
        "--server.address", host,
        "--server.port", str(port),
        "--server.headless", "true" if not debug else "false"
    ]
    
    if not debug:
        cmd.extend(["--logger.level", "warning"])
    
    try:
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        processes.append(process)
        logger.info(f"Streamlit app started with PID {process.pid}")
        
        # Wait for service to be ready
        if wait_for_service(host, port):
            return process
        else:
            logger.error("Streamlit app failed to start properly")
            process.terminate()
            return None
    
    except Exception as e:
        logger.error(f"Failed to start Streamlit app: {e}")
        return None


def monitor_processes():
    """Monitor running processes and restart if needed."""
    while True:
        try:
            for i, process in enumerate(processes):
                if process.poll() is not None:  # Process has terminated
                    logger.error(f"Process {process.pid} has terminated unexpectedly")
                    # Could implement restart logic here if needed
            
            time.sleep(5)  # Check every 5 seconds
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            logger.error(f"Error monitoring processes: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Launch GretahAI ScriptWeaver with Interactive Selector API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python start_with_api.py
    python start_with_api.py --streamlit-port 8501 --api-port 8502
    python start_with_api.py --debug
    python start_with_api.py --api-only
    python start_with_api.py --streamlit-only
        """
    )
    
    parser.add_argument(
        "--streamlit-host",
        default="127.0.0.1",
        help="Streamlit host (default: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--streamlit-port",
        type=int,
        default=8501,
        help="Streamlit port (default: 8501)"
    )
    
    parser.add_argument(
        "--api-host",
        default="127.0.0.1",
        help="API service host (default: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--api-port",
        type=int,
        default=8502,
        help="API service port (default: 8502)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode for both services"
    )
    
    parser.add_argument(
        "--api-only",
        action="store_true",
        help="Start only the API service"
    )
    
    parser.add_argument(
        "--streamlit-only",
        action="store_true",
        help="Start only the Streamlit app"
    )
    
    args = parser.parse_args()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("🚀 GretahAI ScriptWeaver Launcher")
    logger.info("=" * 50)
    
    try:
        # Start API service (unless streamlit-only)
        if not args.streamlit_only:
            api_process = start_api_service(
                host=args.api_host,
                port=args.api_port,
                debug=args.debug
            )
            
            if not api_process:
                logger.error("Failed to start API service")
                sys.exit(1)
        
        # Start Streamlit app (unless api-only)
        if not args.api_only:
            streamlit_process = start_streamlit_app(
                host=args.streamlit_host,
                port=args.streamlit_port,
                debug=args.debug
            )
            
            if not streamlit_process:
                logger.error("Failed to start Streamlit app")
                cleanup_processes()
                sys.exit(1)
        
        # Display startup information
        logger.info("✅ All services started successfully!")
        logger.info("=" * 50)
        
        if not args.api_only:
            logger.info(f"🌐 Streamlit App: http://{args.streamlit_host}:{args.streamlit_port}")
        
        if not args.streamlit_only:
            logger.info(f"🔧 API Service: http://{args.api_host}:{args.api_port}")
        
        logger.info("=" * 50)
        logger.info("Press Ctrl+C to stop all services")
        
        # Monitor processes
        monitor_processes()
    
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        cleanup_processes()
        logger.info("All services stopped")


if __name__ == "__main__":
    main()
