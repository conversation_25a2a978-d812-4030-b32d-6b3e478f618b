# Interactive Element Selector API Service Dependencies
# These packages are required for the standalone API service

# Core API framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# HTTP client for API communication
requests>=2.31.0

# Async support
asyncio-compat>=0.1.2

# Data validation and serialization
pydantic>=2.5.0

# CORS middleware (included with FastAPI but listed for clarity)
# python-multipart>=0.0.6  # For form data handling if needed

# Existing GretahAI dependencies (should already be installed)
# selenium>=4.15.0
# streamlit>=1.28.0

# Optional: Development and testing
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# httpx>=0.25.0  # For testing FastAPI endpoints
