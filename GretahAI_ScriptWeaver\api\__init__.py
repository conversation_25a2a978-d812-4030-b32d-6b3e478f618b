"""
Interactive Element Selector API Package

This package provides a standalone API service for interactive element selection
functionality in GretahAI ScriptWeaver. It isolates the browser automation
from the main Streamlit application to prevent interference and state conflicts.

Components:
- interactive_selector_service.py: FastAPI service for element selection
- selector_client.py: Client library for communicating with the API
- start_selector_api.py: Service launcher script

© 2025 Cogniron All Rights Reserved.
"""

from .selector_client import (
    SelectorAPIClient,
    select_element_interactively_api,
    store_api_selector_result,
    clear_api_selector_flags
)

__all__ = [
    'SelectorAPIClient',
    'select_element_interactively_api',
    'store_api_selector_result',
    'clear_api_selector_flags'
]

__version__ = "1.0.0"
