#!/usr/bin/env python3
"""
Test script for Interactive Element Selector API integration.

This script tests the API-based interactive element selector functionality
to ensure it works correctly with the GretahAI ScriptWeaver application.

Usage:
    python test_selector_api_integration.py [--api-host HOST] [--api-port PORT]

© 2025 Cogniron All Rights Reserved.
"""

import argparse
import logging
import sys
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_api_health(host="127.0.0.1", port="8502"):
    """Test if the API service is running and healthy."""
    try:
        from api.selector_client import SelectorAPIClient
        
        client = SelectorAPIClient(host=host, port=port)
        
        logger.info(f"Testing API health at {host}:{port}...")
        is_healthy = client.health_check()
        
        if is_healthy:
            logger.info("✅ API service is healthy and running")
            return True
        else:
            logger.error("❌ API service health check failed")
            return False
    
    except Exception as e:
        logger.error(f"❌ Error testing API health: {e}")
        return False


def test_session_management(host="127.0.0.1", port="8502"):
    """Test session creation and management."""
    try:
        from api.selector_client import SelectorAPIClient
        
        client = SelectorAPIClient(host=host, port=port)
        
        logger.info("Testing session management...")
        
        # Start a test session
        success, session_id, message = client.start_selector_session(
            url="https://example.com",
            description="Test element selection",
            gap_id="test_gap_001"
        )
        
        if not success:
            logger.error(f"❌ Failed to start session: {message}")
            return False
        
        logger.info(f"✅ Session started successfully: {session_id}")
        
        # Check session status
        success, session_data, error = client.get_session_status(session_id)
        
        if not success:
            logger.error(f"❌ Failed to get session status: {error}")
            return False
        
        logger.info(f"✅ Session status retrieved: {session_data.get('status')}")
        
        # Clean up session
        deleted = client.delete_session(session_id)
        
        if deleted:
            logger.info("✅ Session deleted successfully")
        else:
            logger.warning("⚠️ Failed to delete session")
        
        return True
    
    except Exception as e:
        logger.error(f"❌ Error testing session management: {e}")
        return False


def test_list_sessions(host="127.0.0.1", port="8502"):
    """Test listing active sessions."""
    try:
        from api.selector_client import SelectorAPIClient
        
        client = SelectorAPIClient(host=host, port=port)
        
        logger.info("Testing session listing...")
        
        success, sessions, error = client.list_active_sessions()
        
        if not success:
            logger.error(f"❌ Failed to list sessions: {error}")
            return False
        
        logger.info(f"✅ Listed {len(sessions)} active sessions")
        
        return True
    
    except Exception as e:
        logger.error(f"❌ Error testing session listing: {e}")
        return False


def test_gap_analysis_integration():
    """Test integration with gap analysis module."""
    try:
        logger.info("Testing gap analysis integration...")
        
        # Test locator extraction function
        from core.gap_analysis import _extract_best_locator
        
        # Mock element data
        test_element = {
            'id': 'test-button',
            'name': 'submit',
            'css_selector': '#test-button',
            'xpath': '//button[@id="test-button"]',
            'class_name': 'btn btn-primary',
            'tag_name': 'button'
        }
        
        locator = _extract_best_locator(test_element)
        
        if locator:
            logger.info(f"✅ Locator extraction successful: {locator}")
            return True
        else:
            logger.error("❌ Locator extraction failed")
            return False
    
    except Exception as e:
        logger.error(f"❌ Error testing gap analysis integration: {e}")
        return False


def test_client_error_handling(host="127.0.0.1", port="8502"):
    """Test client error handling with invalid requests."""
    try:
        from api.selector_client import SelectorAPIClient
        
        client = SelectorAPIClient(host=host, port=port)
        
        logger.info("Testing client error handling...")
        
        # Test invalid session ID
        success, session_data, error = client.get_session_status("invalid-session-id")
        
        if not success and "not found" in error.lower():
            logger.info("✅ Invalid session ID handled correctly")
        else:
            logger.warning("⚠️ Invalid session ID error handling may need improvement")
        
        # Test invalid URL
        success, session_id, message = client.start_selector_session(
            url="invalid-url",
            description="Test invalid URL"
        )
        
        # This should still succeed at the API level (URL validation happens in browser)
        if success:
            logger.info("✅ Invalid URL handled at API level")
            # Clean up
            client.delete_session(session_id)
        
        return True
    
    except Exception as e:
        logger.error(f"❌ Error testing client error handling: {e}")
        return False


def run_comprehensive_test(host="127.0.0.1", port="8502"):
    """Run all tests and report results."""
    logger.info("=" * 60)
    logger.info("Interactive Element Selector API Integration Test")
    logger.info("=" * 60)
    
    tests = [
        ("API Health Check", lambda: test_api_health(host, port)),
        ("Session Management", lambda: test_session_management(host, port)),
        ("Session Listing", lambda: test_list_sessions(host, port)),
        ("Gap Analysis Integration", test_gap_analysis_integration),
        ("Error Handling", lambda: test_client_error_handling(host, port))
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! API integration is working correctly.")
        return True
    else:
        logger.error(f"⚠️ {total - passed} test(s) failed. Please check the issues above.")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test Interactive Element Selector API integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python test_selector_api_integration.py
    python test_selector_api_integration.py --api-host 127.0.0.1 --api-port 8503
        """
    )
    
    parser.add_argument(
        "--api-host",
        default="127.0.0.1",
        help="API service host (default: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--api-port",
        default="8502",
        help="API service port (default: 8502)"
    )
    
    args = parser.parse_args()
    
    # Run the comprehensive test
    success = run_comprehensive_test(args.api_host, args.api_port)
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
