"""
Stage 10: Script Playground for GretahAI ScriptWeaver

This module provides a completely independent script experimentation and generation utility.
It provides functionality for:
- Loading optimized scripts as templates
- Selecting target test cases for generation
- Template-based AI script generation using proven patterns
- Managing generated scripts with proper metadata
- Independent access regardless of workflow stage
- Internal Google AI API key management (completely independent from Stage 2)

Key Features:
- **Completely Independent**: No dependencies on other stages, including Stage 2 API configuration
- **Automatic API Key Loading**: Automatically loads Google AI API key from config.json on startup
- **Background API Management**: Invisible API key management with automatic loading from config.json and environment variables
- **No Manual Configuration**: API key management is completely hidden from the user interface
- **Always Accessible**: Can be accessed at any time, regardless of workflow stage
- **Template-Based Generation**: Uses optimized scripts as templates for new test cases
- **AI-Powered Adaptation**: Leverages Google AI to adapt templates to new requirements
- **Quality Preservation**: Maintains optimization patterns and best practices from templates
- **Playground Nature**: Functions as an experimental environment for script generation

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Modular AI integration via core.ai_template module
- Independent API key management with secure storage

Functions:
    stage10_script_playground(state): Main Stage 10 function for script playground
    load_stage10_google_api_key(): Automatic API key loading from config.json and environment
    _validate_stage10_prerequisites(state): Validate Stage 10 specific prerequisites
"""

import os
import json
import logging
import streamlit as st
from datetime import datetime, timedelta
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

# Import core dependencies
from state_manager import StateStage
from debug_utils import debug

# Import enhanced Stage 10 logging
from core.stage10_logger import (
    get_stage10_logger, log_gap_analysis, log_interactive_selector,
    log_session_state, log_ui_interaction, log_process, log_ai_request,
    log_operation, log_script_generation, display_debug_panel, export_logs
)

# Import configuration and debug utilities
from core.config import APP_CONFIG_FILE

# Import helper functions
from core.template_helpers import (
    get_optimized_scripts_for_templates,
    format_template_script_display,
    get_available_test_cases,
    format_test_case_display,
    validate_template_generation_inputs,
    create_template_generation_filename
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10")

# Constants for Stage 10
RECENT_SCRIPTS_TIME_LIMIT_HOURS = 1
MAX_RECENT_SCRIPTS_DISPLAY = 3
STAGE10_EXECUTION_TIMEOUT = 300  # 5 minutes


@st.cache_data
def load_stage10_google_api_key():
    """
    Load Google API key for Stage 10 from config file or environment variable.

    This function provides automatic loading of the API key specifically for Stage 10,
    maintaining independence from other stages while using the same config.json file.
    Uses caching to avoid repeated file I/O operations for better performance.
    Cache is automatically invalidated when the config file changes.

    Returns:
        str: The Google API key if found, empty string otherwise
    """
    logger.debug("[stage10.render] Stage 10: Loading Google API key (cached)")

    # Try to load from config file first
    try:
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, "r") as f:
                config = json.load(f)
                if "google_api_key" in config and config["google_api_key"]:
                    logger.debug("[stage10.render] Stage 10: API key loaded from config file")
                    return config["google_api_key"]
    except Exception as e:
        logger.error(f"Stage 10: Error loading config file: {e}")
        logger.debug(f"[stage10.render] Stage 10: Config file error: {e}")

    # Fall back to environment variable
    env_key = os.environ.get("GOOGLE_API_KEY", "")
    if env_key:
        logger.debug("[stage10.render] Stage 10: API key loaded from environment variable")
    else:
        logger.debug("[stage10.render] Stage 10: No API key found in config or environment")
    return env_key





def _validate_stage10_prerequisites(state) -> tuple[bool, str]:
    """
    Validate prerequisites for Stage 10 operations.
    Stage 10 is completely independent and manages its own Google AI API key.
    Checks both automatic loading from config.json and manual input sources.

    Args:
        state: StateManager instance

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check for Google AI API key from multiple sources
        # 1. Check Stage 10's internal state
        state_api_key = getattr(state, 'stage10_google_api_key', '')

        # 2. Check automatic loading from config/environment
        config_api_key = load_stage10_google_api_key()

        # Use any available API key
        available_api_key = state_api_key or config_api_key

        if not available_api_key:
            return False, "Google AI API key is required for script generation"

        # Ensure the state has the API key for later use
        if not state_api_key and config_api_key:
            state.stage10_google_api_key = config_api_key
            logger.debug("[stage10.render] Stage 10: API key loaded from config into state during validation")

        # Check script storage initialization
        if not hasattr(state, '_script_storage') or state._script_storage is None:
            return False, "Script storage not initialized"

        return True, ""

    except Exception as e:
        logger.debug(f"[stage10.render] Stage 10 prerequisite validation error: {e}")
        return False, f"Validation error: {e}"


def _get_cached_recent_scripts(state) -> List[Dict[str, Any]]:
    """
    Get recently generated scripts with caching for performance.

    Args:
        state: StateManager instance

    Returns:
        List of recent template-generated scripts
    """
    try:
        # Check if we have cached results that are still fresh
        cache_key = '_stage10_recent_scripts_cache'
        cache_timestamp_key = '_stage10_cache_timestamp'

        if (hasattr(state, cache_key) and hasattr(state, cache_timestamp_key) and
            getattr(state, cache_timestamp_key) and
            (datetime.now() - getattr(state, cache_timestamp_key)).seconds < 30):  # 30 second cache
            logger.debug("[stage10.render] Stage 10: Using cached recent scripts")
            return getattr(state, cache_key)

        # Get fresh data
        if not hasattr(state, '_script_storage') or not state._script_storage:
            return []

        all_scripts = state._script_storage.get_all_scripts()
        time_limit = datetime.now() - timedelta(hours=RECENT_SCRIPTS_TIME_LIMIT_HOURS)

        recent_scripts = []
        for script in all_scripts:
            if (script.get('type') == 'template_generated' and
                script.get('metadata', {}).get('template_based', False)):

                timestamp_str = script.get('metadata', {}).get('generation_timestamp')
                if timestamp_str:
                    try:
                        script_time = datetime.fromisoformat(timestamp_str)
                        if script_time > time_limit:
                            recent_scripts.append(script)
                    except Exception:
                        continue

        # Sort by generation time (most recent first)
        recent_scripts.sort(
            key=lambda x: x.get('metadata', {}).get('generation_timestamp', ''),
            reverse=True
        )

        # Cache the results
        setattr(state, cache_key, recent_scripts)
        setattr(state, cache_timestamp_key, datetime.now())

        logger.debug(f"[stage10.render] Stage 10: Found {len(recent_scripts)} recent scripts")
        return recent_scripts

    except Exception as e:
        logger.debug(f"[stage10.render] Error getting recent scripts: {e}")
        return []


def _cleanup_stage10_session_state():
    """
    Clean up stale Stage 10 session state keys to prevent memory leaks and state corruption.

    This function removes temporary session state keys that may accumulate over time,
    including gap analysis cache, preview states, and execution flags.
    """
    try:
        logger.debug("[stage10.render] Stage 10: Starting session state cleanup")

        # Keys to clean up (patterns)
        cleanup_patterns = [
            'gap_analysis_',
            'gap_form_submitted_',
            'interactive_selector_in_progress_',
            'interactive_selector_pending_',
            'interactive_selector_polling_',
            'interactive_selector_result_file_',
            'interactive_selector_start_time_',
            'interactive_locator_',
            'pending_interactive_gap_',
            'show_preview_',
            'show_generated_preview'
        ]

        # Find keys to remove
        keys_to_remove = []
        for key in st.session_state.keys():
            for pattern in cleanup_patterns:
                if key.startswith(pattern):
                    # Check if key is stale (older than 1 hour)
                    if _is_session_key_stale(key):
                        keys_to_remove.append(key)
                    break

        # Remove stale keys
        for key in keys_to_remove:
            try:
                del st.session_state[key]
                logger.debug(f"[stage10.render] Stage 10: Cleaned up stale session key: {key}")
            except KeyError:
                # Key already removed, ignore
                pass

        if keys_to_remove:
            logger.debug(f"[stage10.render] Stage 10: Cleaned up {len(keys_to_remove)} stale session state keys")
        else:
            logger.debug("[stage10.render] Stage 10: No stale session state keys found")

    except Exception as e:
        logger.debug(f"[stage10.render] Stage 10: Error during session state cleanup: {e}")


def _is_session_key_stale(key: str) -> bool:
    """
    Check if a session state key is stale and should be cleaned up.

    Args:
        key: Session state key to check

    Returns:
        bool: True if the key is stale and should be removed
    """
    try:
        # For now, consider all temporary keys as candidates for cleanup
        # In the future, we could add timestamp-based logic here

        # Always clean up preview states (they should be short-lived)
        if 'show_preview_' in key or 'show_generated_preview' in key:
            return True

        # Clean up in-progress flags (they should not persist across sessions)
        # BUT preserve ALL interactive selector related flags to prevent interference
        if 'in_progress_' in key and not key.startswith('interactive_selector_'):
            return True

        # Only clean up non-interactive selector pending flags
        if 'pending_' in key and not key.startswith('interactive_selector_pending_'):
            return True

        # Never clean up interactive selector related keys during active sessions
        if key.startswith(('interactive_selector_', 'interactive_locator_')):
            return False

        # Keep gap analysis results for the current session
        if 'gap_analysis_' in key:
            return False

        # Keep Stage 10 configuration (website URL should persist)
        if key == 'stage10_website_url_input':
            return False

        # Default to not stale for safety
        return False

    except Exception:
        # If we can't determine, err on the side of caution
        return False


def stage10_script_playground(state):
    """
    Stage 10: Script Playground.

    This stage provides an independent, always-accessible script experimentation and generation utility.
    It loads optimized scripts as templates and generates new scripts for different test cases,
    preserving the optimization patterns and best practices from the templates.

    Args:
        state (StateManager): The application state manager instance
    """

    _check_and_launch_pending_interactive_selectors()

    with log_operation("stage10_script_playground", "stage10"):
        
        _check_and_launch_pending_interactive_selectors()
        # CRITICAL: Check for interactive selector activity FIRST to prevent all processing
      
        interactive_selector_active = (
            st.session_state.get('interactive_selector_in_progress', False)
            or any(k.startswith('interactive_locator_')  for k in st.session_state)
            or any(k.startswith('interactive_selector_polling_') for k in st.session_state)
        )

        # Add comprehensive debugging for rerun detection
        logger.debug(f"[stage10.render] Stage 10 render started - session state keys: {len(st.session_state.keys())}")
        logger.debug(f"[stage10.render] Interactive selector in progress: {st.session_state.get('interactive_selector_in_progress', False)}")
        logger.debug(f"[stage10.render] Interactive locator keys: {[k for k in st.session_state.keys() if k.startswith('interactive_locator_')]}")
        logger.debug(f"[stage10.render] Interactive selector pending keys: {[k for k in st.session_state.keys() if k.startswith('interactive_selector_pending_')]}")
        logger.debug(f"[stage10.render] Interactive selector polling keys: {[k for k in st.session_state.keys() if k.startswith('interactive_selector_polling_')]}")
        logger.debug(f"[stage10.render] Interactive selector active (combined): {interactive_selector_active}")

        # If interactive selector is active, show minimal UI and return immediately
        if interactive_selector_active:
            logger.debug("[stage10.render] Interactive selector active - showing minimal UI and returning immediately")
            st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
            st.info("🎯 Interactive element selector is active. Please complete the element selection in the browser window.")
            st.markdown("*The page will automatically update once element selection is complete.*")
            return

        st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
        st.markdown("*Experiment with script generation using optimized scripts as templates*")

        # Log stage access with detailed context
        log_ui_interaction("stage_accessed", "stage10_script_playground",
                          state_id=id(state),
                          session_state_id=id(st.session_state.get('state', None)))

        logger.debug("[stage10.render] Script Playground accessed")
        logger.debug(f"[stage10.render] State object ID: {id(state)}")
        logger.debug(f"[stage10.render] Session state object ID: {id(st.session_state.get('state', None))}")

        # Clean up stale session state on entry (interactive selector already checked above)
        with log_operation("cleanup_session_state", "stage10.cleanup"):
            _cleanup_stage10_session_state()

    # Validate prerequisites first
    is_valid, error_msg = _validate_stage10_prerequisites(state)
    if not is_valid:
        if "API key" in error_msg:
            st.error("🔑 **API Key Required**")
            st.info("Please ensure your Google AI API key is properly configured in the `config.json` file or as an environment variable (`GOOGLE_API_KEY`).")
            st.markdown("**Config file location:** `config.json` in the application root directory")
            st.markdown("**Required format:**")
            st.code('{\n  "google_api_key": "your-api-key-here"\n}', language='json')
            return
        else:
            st.warning(f"⚠️ **Prerequisites Missing**: {error_msg}")
            return

    # Check for pending interactive selector requests and launch them
    _check_and_launch_pending_interactive_selectors()

    # Process pending script executions (interactive selector already checked at function start)
    _check_and_execute_pending_scripts(state)

    # Display execution results compactly if available
    if hasattr(state, 'stage10_execution_results') and state.stage10_execution_results:
        _display_stage10_execution_results_compact(state)

    # Initialize script storage if needed
    if not hasattr(state, '_script_storage') or state._script_storage is None:
        state._init_script_storage()

    # Load optimized scripts for templates with error handling
    try:
        optimized_scripts = get_optimized_scripts_for_templates(state._script_storage)
        available_test_cases = get_available_test_cases(state)
    except Exception as e:
        st.error(f"❌ **Data Loading Error**: {e}")
        logger.debug(f"[stage10.render] Stage 10 data loading error: {e}")
        return

    # Quick status overview
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Templates", len(optimized_scripts))
    with col2:
        st.metric("Test Cases", len(available_test_cases))
    with col3:
        recent_scripts = _get_cached_recent_scripts(state)
        st.metric("Recent Scripts", len(recent_scripts))

    # Stage 10 Independent Configuration Section
    with st.expander("⚙️ Configuration", expanded=False):
        st.markdown("**Website URL for Interactive Element Selection:**")
        st.markdown("*Configure the target website URL for interactive element selector functionality*")

        # Initialize Stage 10 website URL if not set
        if not hasattr(state, 'stage10_website_url'):
            state.stage10_website_url = "https://example.com"

        # Website URL input field
        new_website_url = st.text_input(
            "Website URL",
            value=state.stage10_website_url,
            placeholder="https://your-target-website.com",
            help="Enter the base URL of the website you want to test. This URL will be used for interactive element selection during gap analysis.",
            key="stage10_website_url_input"
        )

        # Update state if URL changed (ONLY update session state if actually changed)
        # BUT skip this update if interactive selector is active to prevent interference
        if new_website_url != state.stage10_website_url and not interactive_selector_active:
            state.stage10_website_url = new_website_url
            logger.debug(f"[stage10.render] Stage 10: Website URL updated to: {new_website_url}")
            st.session_state['state'] = state
            logger.debug("[stage10.render] Stage 10: Session state updated due to URL change - this will trigger a rerun")
        elif interactive_selector_active:
            logger.debug(f"[stage10.render] Stage 10: Skipping URL update during interactive selector - URL: {new_website_url}")
        else:
            logger.debug(f"[stage10.render] Stage 10: Website URL unchanged: {new_website_url}")

    # Check if we have the necessary data
    if not optimized_scripts:
        st.info("🎮 **No Templates Available**")
        st.markdown("Create optimized scripts first to use as templates.")

        # Compact navigation
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary", key="stage10_start_new_project"):
                logger.debug("[stage10.render] User navigated to Stage 1 from Script Playground - no templates")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Playground")
                st.rerun()
                return
        with col2:
            if st.button("🔧 Create Templates", use_container_width=True, key="stage10_create_templates"):
                logger.debug("[stage10.render] User navigated to Stage 8 from Script Playground - no templates")
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to optimization from Script Playground")
                st.rerun()
                return
        return

    if not available_test_cases:
        st.warning("⚠️ **No Test Cases Available**")
        if st.button("📁 Upload Test Cases", use_container_width=True, type="primary", key="stage10_upload_test_cases"):
            logger.debug("[stage10.render] User navigated to Stage 1 from Script Playground - no test cases")
            state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to upload from Script Playground")
            st.rerun()
            return
        return

    # Main Script Generation Workflow (Streamlined)
    with st.expander("🚀 Script Generation", expanded=True):
        # Combined template and test case selection in compact layout
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Template:**")
            # Create template options
            template_options = []
            template_map = {}

            for script in optimized_scripts:
                display_info = format_template_script_display(script)
                option_text = f"{display_info['title']} ({display_info['timestamp']})"
                template_options.append(option_text)
                template_map[option_text] = script

            selected_template_option = st.selectbox(
                "Select optimized script template",
                template_options,
                key="template_selection",
                label_visibility="collapsed"
            )
            selected_template = template_map[selected_template_option]

        with col2:
            st.markdown("**Target Test Case:**")
            # Create test case options
            test_case_options = []
            test_case_map = {}

            for test_case in available_test_cases:
                option_text = format_test_case_display(test_case)
                test_case_options.append(option_text)
                test_case_map[option_text] = test_case

            selected_test_case_option = st.selectbox(
                "Select target test case",
                test_case_options,
                key="test_case_selection",
                label_visibility="collapsed"
            )
            selected_test_case = test_case_map[selected_test_case_option]

        # Preview sections for selected template and test case
        _display_selection_previews(selected_template, selected_test_case)

        # Generation options directly in the main section (no nested expander)
        st.markdown("---")
        st.markdown("**Generation Options:**")

        col1, col2 = st.columns(2)

        with col1:
            custom_instructions = st.text_area(
                "Custom Instructions",
                placeholder="Optional: Add specific requirements...",
                key="template_custom_instructions",
                height=80
            )

        with col2:
            st.markdown("**Settings:**")
            preserve_structure = st.checkbox("Preserve Template Structure", value=True, key="preserve_structure")
            include_error_handling = st.checkbox("Include Error Handling", value=True, key="include_error_handling")

        # Primary action button with error boundary
        st.markdown("---")
        if st.button("🚀 Generate Script", use_container_width=True, type="primary", key="stage10_generate_from_template"):
            try:
                # Validate inputs
                is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)
                if not is_valid:
                    st.error(f"❌ {error_message}")
                    return

                # Call generation function with comprehensive error handling
                _generate_script_from_template(
                    state, selected_template, selected_test_case,
                    custom_instructions, preserve_structure, include_error_handling
                )
            except Exception as e:
                error_msg = f"Unexpected error during script generation: {e}"
                st.error(f"❌ **Generation Error**: {error_msg}")
                logger.debug(f"[stage10.render] Stage 10 generation button error: {e}")
                # Don't return here - let the UI continue to function

    # Check if gap analysis is in progress or form is active and prevent further UI rendering
    gap_analysis_in_progress = any(key.endswith('_in_progress') and st.session_state.get(key, False)
                                  for key in st.session_state.keys() if 'gap_analysis_' in key)

    # Also check for active gap analysis results (form is displayed)
    gap_analysis_active = any(key.startswith('gap_analysis_') and not key.endswith('_in_progress')
                             and st.session_state.get(key) is not None
                             for key in st.session_state.keys())

    if gap_analysis_in_progress:
        logger.debug("[stage10.render] Stage 10: Gap analysis in progress, preventing further UI rendering to avoid automatic reruns")
        st.info("🔍 Gap analysis in progress. Please wait for the analysis to complete and fill in the form.")
        return
    elif gap_analysis_active:
        logger.debug("[stage10.render] Stage 10: Gap analysis form is active, preventing further UI rendering to avoid automatic reruns")
        st.info("📝 Gap analysis form is displayed above. Please fill in the required information to continue.")
        return

    # Display generated script preview if requested
    if st.session_state.get('show_generated_preview'):
        st.markdown("### 👁️ Generated Script Preview")
        col1, col2 = st.columns([4, 1])
        with col1:
            st.code(st.session_state['show_generated_preview'], language='python')
        with col2:
            if st.button("❌ Close Preview", key="close_generated_preview", use_container_width=True):
                del st.session_state['show_generated_preview']
                logger.debug("[stage10.render] Stage 10: Close generated preview button clicked, triggering rerun")
                st.rerun()
                return
        st.markdown("---")

    # Persistent Script Execution Section (shows recently generated scripts)
    _display_persistent_execution_section(state)

    # Display debug panel if debug mode is enabled
    display_debug_panel()

    # Optional workflow navigation (minimalist)
    with st.expander("🧭 Quick Navigation", expanded=False):
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📁 Upload CSV (Stage 1)", use_container_width=True, key="stage10_nav_upload_csv"):
                logger.debug("[stage10.render] User navigated to Stage 1 from Script Playground")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Playground")
                st.rerun()
                return

        with col2:
            if st.button("🔧 Optimize Scripts (Stage 8)", use_container_width=True, key="stage10_nav_optimize_scripts"):
                logger.debug("[stage10.render] User navigated to Stage 8 from Script Playground")
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to Stage 8 from Script Playground")
                st.rerun()
                return

        with col3:
            if st.button("📜 Script Browser (Stage 9)", use_container_width=True, key="stage10_nav_script_browser"):
                logger.debug("[stage10.render] User navigated to Stage 9 from Script Playground")
                state.advance_to(StateStage.STAGE9_BROWSE, "User navigated to Stage 9 from Script Playground")
                st.rerun()
                return


def _generate_script_from_template(state, template_script, target_test_case,
                                 custom_instructions, preserve_structure, include_error_handling):
    """
    Generate a new script using the selected template and test case with pre-generation gap analysis.

    This function now includes a pre-generation validation and gap analysis workflow:
    1. Analyzes template script and target test case to identify gaps
    2. Prompts user to fill in missing information if gaps are found
    3. Generates script with complete information for better quality

    Args:
        state: StateManager instance
        template_script: Selected template script
        target_test_case: Target test case for generation
        custom_instructions: User's custom instructions
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
    """
    try:
        logger.debug("[stage10.render] Starting template-based script generation with gap analysis")

        # Import AI functions inside the function to avoid circular imports
        from core.ai_template import generate_template_based_script
        from core.gap_analysis import analyze_template_gaps, display_gap_filling_form

        # Validate inputs before AI call
        is_valid, validation_error = validate_template_generation_inputs(template_script, target_test_case)
        if not is_valid:
            st.error(f"❌ **Validation Error**: {validation_error}")
            logger.debug(f"[stage10.render] Template generation validation failed: {validation_error}")
            return

        # Step 1: Pre-Generation Gap Analysis
        logger.debug("[stage10.render] Starting pre-generation gap analysis")

        # Check if we already have gap analysis results in session state
        gap_analysis_key = f"gap_analysis_{template_script.get('id', 'unknown')}_{target_test_case.get('Test Case ID', 'unknown')}"
        gap_analysis_in_progress_key = f"{gap_analysis_key}_in_progress"

        # Prevent duplicate gap analysis calls
        if st.session_state.get(gap_analysis_in_progress_key, False):
            logger.debug("[stage10.render] Gap analysis already in progress, waiting...")
            st.info("🔍 Gap analysis in progress, please wait...")
            return

        if gap_analysis_key not in st.session_state:
            # Mark gap analysis as in progress
            st.session_state[gap_analysis_in_progress_key] = True

            with st.spinner("🔍 Analyzing template and test case for gaps..."):
                gap_analysis_result = analyze_template_gaps(
                    template_script=template_script,
                    target_test_case=target_test_case,
                    api_key=getattr(state, 'stage10_google_api_key', None),
                    model_name="gemini-2.0-flash"
                )

                if gap_analysis_result:
                    st.session_state[gap_analysis_key] = gap_analysis_result
                    logger.debug(f"[stage10.render] Gap analysis completed, found {len(gap_analysis_result.get('gaps', []))} gaps")
                else:
                    st.error("❌ Failed to perform gap analysis. Proceeding with basic generation.")
                    logger.debug("[stage10.render] Gap analysis failed, proceeding without gap filling")
                    st.session_state[gap_analysis_key] = {'gaps': [], 'analysis_failed': True}

                # Clear the in-progress flag
                st.session_state[gap_analysis_in_progress_key] = False

        gap_analysis_result = st.session_state[gap_analysis_key]

        # Step 2: Display Gap Filling Form if gaps are found
        gaps_found = gap_analysis_result.get('gaps', [])
        user_provided_data = {}

        if gaps_found and not gap_analysis_result.get('analysis_failed', False):
            st.markdown("### 🔧 Missing Information Detected")
            st.info(f"Found {len(gaps_found)} gaps that need to be filled for optimal script generation.")

            # Get Stage 10 specific website URL for interactive element selection
            website_url = getattr(state, 'stage10_website_url', None)

            # Display the gap filling form with website URL for interactive selector
            user_provided_data = display_gap_filling_form(gaps_found, gap_analysis_key, website_url)

            # If form is not complete, return early
            if user_provided_data is None:
                logger.debug("[stage10.render] Gap filling form not completed, waiting for user input")
                return

            logger.debug(f"[stage10.render] User provided data for {len(user_provided_data)} gaps")

        # Step 3: Generate Script with Enhanced Context
        with st.spinner("🤖 Generating script from template..."):
            logger.debug("[stage10.render] Calling modular AI function for template-based script generation with gap data")

            # Track generation time for performance monitoring
            start_time = datetime.now()

            # Enhance custom instructions with gap-filling data
            enhanced_instructions = custom_instructions or ""
            if user_provided_data:
                gap_instructions = "\n\nGap Analysis Data:\n"
                for gap_id, value in user_provided_data.items():
                    gap_info = next((g for g in gaps_found if g.get('id') == gap_id), {})
                    gap_description = gap_info.get('description', gap_id)
                    gap_instructions += f"- {gap_description}: {value}\n"

                enhanced_instructions += gap_instructions
                logger.debug(f"[stage10.render] Enhanced instructions with {len(user_provided_data)} gap-filling entries")

            generated_script = generate_template_based_script(
                template_script=template_script,
                target_test_case=target_test_case,
                custom_instructions=enhanced_instructions,
                preserve_structure=preserve_structure,
                include_error_handling=include_error_handling,
                website_url=getattr(state, 'stage10_website_url', None),
                api_key=getattr(state, 'stage10_google_api_key', None),
                model_name="gemini-2.0-flash"
            )

            # Log generation time
            generation_time = (datetime.now() - start_time).total_seconds()
            logger.debug(f"[stage10.render] Template-based script generation completed in {generation_time:.2f} seconds")

            if generated_script and generated_script.strip():
                # Clear the gap analysis from session state after successful generation
                if gap_analysis_key in st.session_state:
                    del st.session_state[gap_analysis_key]

                _handle_successful_generation(state, template_script, target_test_case, generated_script)
            else:
                st.error("❌ Failed to generate script. Please try again.")
                logger.debug("[stage10.render] Template-based script generation failed - no script returned")

    except ImportError as e:
        error_msg = f"AI module import failed: {e}"
        st.error(f"❌ **Import Error**: {error_msg}")
        logger.debug(f"[stage10.render] Template-based script generation import error: {e}")
    except Exception as e:
        error_msg = f"Template-based script generation failed: {e}"
        st.error(f"❌ **Generation Error**: {error_msg}")
        logger.debug(f"[stage10.render] Template-based script generation error: {e}")


def _handle_successful_generation(state, template_script, target_test_case, generated_script):
    """
    Handle successful script generation with streamlined display and storage.

    Args:
        state: StateManager instance
        template_script: Template script used
        target_test_case: Target test case
        generated_script: Generated script content
    """
    try:
        logger.debug("[stage10.render] Handling successful template-based script generation")

        # Create filename
        filename = create_template_generation_filename(template_script, target_test_case)

        # Compact success display
        st.success(f"✅ Script generated for **{target_test_case.get('Test Case ID', 'Unknown')}**")

        # Streamlined action buttons
        col1, col2, col3 = st.columns(3)
        with col1:
            st.download_button(
                label="📥 Download",
                data=generated_script,
                file_name=filename,
                mime="text/x-python",
                use_container_width=True
            )

        with col2:
            if st.button("👁️ Preview", use_container_width=True, key="stage10_preview_generated"):
                # Store preview state to show outside
                st.session_state['show_generated_preview'] = generated_script
                logger.debug("[stage10.render] Stage 10: Preview button clicked, triggering rerun")
                st.rerun()
                return

        with col3:
            if st.button("📋 Copy", use_container_width=True, key="stage10_copy_generated"):
                st.code(generated_script, language='python')

        # Save to script storage
        template_metadata = {
            'generation_type': 'template_based',
            'template_script_id': template_script.get('id'),
            'template_test_case_id': template_script.get('test_case_id'),
            'target_test_case_id': target_test_case.get('Test Case ID'),
            'generation_timestamp': datetime.now().isoformat(),
            'template_based': True,
            'optimization_status': 'template_generated'
        }

        state.add_script_to_history(
            script_content=generated_script,
            script_type='template_generated',
            step_no=None,
            file_path=filename,
            metadata=template_metadata
        )

        logger.debug(f"[stage10.render] Template-based script saved with filename: {filename}")

        # Clear cache to refresh recent scripts display
        if hasattr(state, '_stage10_recent_scripts_cache'):
            delattr(state, '_stage10_recent_scripts_cache')
        if hasattr(state, '_stage10_cache_timestamp'):
            delattr(state, '_stage10_cache_timestamp')

    except Exception as e:
        error_msg = f"Failed to handle successful generation: {e}"
        st.error(f"❌ **Storage Error**: {error_msg}")
        logger.debug(f"[stage10.render] Error handling successful generation: {e}")


def _display_persistent_execution_section(state):
    """
    Display a streamlined section for executing recently generated scripts.
    This section shows only essential information and actions.

    Args:
        state (StateManager): The application state manager instance
    """
    # Use cached function for better performance
    recent_scripts = _get_cached_recent_scripts(state)

    if not recent_scripts:
        return

    # Streamlined execution section
    with st.expander("▶️ Recent Scripts", expanded=False):
        # Show up to MAX_RECENT_SCRIPTS_DISPLAY most recent scripts
        display_scripts = recent_scripts[:MAX_RECENT_SCRIPTS_DISPLAY]

        for i, script in enumerate(display_scripts):
            script_id = script.get('id', f'script_{i}')
            metadata = script.get('metadata', {})
            target_test_case_id = metadata.get('target_test_case_id', 'Unknown')
            generation_time = metadata.get('generation_timestamp', '')
            formatted_time = _format_timestamp_safe(generation_time)
            filename = script.get('file_path') or _create_safe_filename(target_test_case_id, formatted_time)

            # Compact layout with essential info only
            col1, col2, col3 = st.columns([3, 1, 1])

            with col1:
                st.markdown(f"**{target_test_case_id}** _{formatted_time}_")

            with col2:
                if st.button("👁️", key=f"preview_{script_id}", help="Preview script", use_container_width=True):
                    # Store preview state to show outside the expander
                    st.session_state[f'show_preview_{script_id}'] = True
                    logger.debug(f"[stage10.render] Stage 10: Preview button clicked for script {script_id}, triggering rerun")
                    st.rerun()
                    return

            with col3:
                if st.button("▶️", key=f"execute_{script_id}", help="Execute script",
                           use_container_width=True, type="secondary"):
                    _handle_script_execution_request(state, script, filename, target_test_case_id)
                    return

    # Display script previews outside of expanders to avoid nesting
    _display_script_previews(recent_scripts)


def _display_script_previews(recent_scripts):
    """
    Display script previews outside of expanders to avoid nesting issues.

    Args:
        recent_scripts: List of recent script dictionaries
    """
    # Check for any preview requests in session state
    for i, script in enumerate(recent_scripts[:MAX_RECENT_SCRIPTS_DISPLAY]):
        script_id = script.get('id', f'script_{i}')
        preview_key = f'show_preview_{script_id}'

        if st.session_state.get(preview_key, False):
            # Display the preview
            metadata = script.get('metadata', {})
            target_test_case_id = metadata.get('target_test_case_id', 'Unknown')

            st.markdown(f"### 👁️ Script Preview: {target_test_case_id}")

            col1, col2 = st.columns([4, 1])
            with col1:
                st.code(script.get('content', 'No content available'), language='python')
            with col2:
                if st.button("❌ Close", key=f"close_preview_{script_id}", use_container_width=True):
                    # Clear the preview state
                    st.session_state[preview_key] = False
                    logger.debug(f"[stage10.render] Stage 10: Close preview button clicked for script {script_id}, triggering rerun")
                    st.rerun()
                    return

            st.markdown("---")


def _check_and_launch_pending_interactive_selectors():
    """
    Check for pending interactive selector requests and launch them.

    This function processes pending interactive selector requests that were set
    by the gap analysis form buttons to avoid rerun prevention interference.
    """
    
    logger.debug(f"[stage10.render] [LAUNCH-DEBUG] run-id={time.time():.0f} "
              f"pending={ [k for k in st.session_state if k.startswith('interactive_selector_pending_')] }")
    
    try:
        logger.debug("[stage10.render] Checking for pending interactive selector requests")
        logger.debug(f"[stage10.render] [LAUNCH-DEBUG] run-id={time.time():.0f} "
              f"pending={ [k for k in st.session_state if k.startswith('interactive_selector_pending_')] }")

        # Find any pending selector requests
        pending_keys = [key for key in st.session_state.keys()
                       if key.startswith("interactive_selector_pending_")]

        logger.debug(f"[stage10.render] Found {len(pending_keys)} pending interactive selector requests")

        if not pending_keys:
            return

        # Process the first pending request (one at a time to avoid conflicts)
        for pending_key in pending_keys:
            gap_id = pending_key.replace("interactive_selector_pending_", "")
            pending_data = st.session_state[pending_key]

            logger.debug(f"[stage10.render] Processing pending interactive selector for gap {gap_id}")

            # Extract data from pending request
            description = pending_data.get('description', 'Element locator')
            website_url = pending_data.get('website_url', '')

            # Clear the pending flag
            del st.session_state[pending_key]

            # Import and call the interactive selector handler
            from core.gap_analysis import _handle_gap_interactive_element_selection

            # Launch the interactive selector
            logger.debug(f"[stage10.render] Launching interactive selector for gap {gap_id}")
            _handle_gap_interactive_element_selection(gap_id, description, website_url)

            # Only process one request at a time
            break

    except Exception as e:
        logger.debug(f"[stage10.render] Error checking pending interactive selectors: {e}")
        st.error(f"❌ Error processing interactive selector request: {e}")


def _handle_script_execution_request(state, script: Dict[str, Any], filename: str, target_test_case_id: str):
    """Handle script execution request with proper state management."""
    logger.debug(f"[stage10.render] Stage 10: Execute button clicked for script {script.get('id', 'unknown')}")

    # Prevent duplicate executions by checking if already pending
    if hasattr(state, 'stage10_execution_pending') and filename in state.stage10_execution_pending:
        logger.debug(f"[stage10.render] Stage 10: Execution already pending for {filename}, ignoring duplicate request")
        return

    # Create target test case data for execution
    target_test_case = {
        'Test Case ID': target_test_case_id,
        'Test Case Objective': f'Execute template-generated script for {target_test_case_id}'
    }

    # Initialize execution pending dict if needed
    if not hasattr(state, 'stage10_execution_pending'):
        state.stage10_execution_pending = {}
        logger.debug("[stage10.render] Stage 10: Created stage10_execution_pending attribute")

    # Add to pending executions
    state.stage10_execution_pending[filename] = {
        'script_filename': filename,
        'script_content': script.get('content', ''),
        'target_test_case': target_test_case,
        'timestamp': datetime.now().isoformat()
    }

    logger.debug(f"[stage10.render] Stage 10: Added pending execution for {filename}")
    logger.debug(f"[stage10.render] Stage 10: Pending executions now: {list(state.stage10_execution_pending.keys())}")

    # Update session state and trigger rerun to start execution
    st.session_state['state'] = state
    logger.debug("[stage10.render] Stage 10: Updated session state, triggering rerun")
    st.rerun()
    return


def _check_and_execute_pending_scripts(state):
    """
    Check for pending script executions and execute them with comprehensive validation.
    This function is called after the button click rerun to actually execute the script.

    Args:
        state (StateManager): The application state manager instance
    """
    try:
        # Quick early exit if no pending executions (reduces debug noise)
        if not hasattr(state, 'stage10_execution_pending') or not state.stage10_execution_pending:
            return

        logger.debug(f"[stage10.render] Stage 10: Checking for pending executions...")
        logger.debug(f"[stage10.render] Stage 10: Pending executions count: {len(state.stage10_execution_pending)}")
        logger.debug(f"[stage10.render] Stage 10: Pending executions: {list(state.stage10_execution_pending.keys())}")

        # Validate execution state before proceeding
        if not _validate_execution_state(state):
            logger.debug("[stage10.render] Stage 10: Execution state validation failed, clearing pending executions")
            state.stage10_execution_pending.clear()
            return

        # Get the first pending execution
        for script_filename, execution_data in list(state.stage10_execution_pending.items()):
            logger.debug(f"[stage10.render] Stage 10: Found pending execution for {script_filename}")

            # Validate execution data
            if not _validate_execution_data(execution_data):
                logger.debug(f"[stage10.render] Stage 10: Invalid execution data for {script_filename}, skipping")
                del state.stage10_execution_pending[script_filename]
                continue

            # Remove from pending list
            del state.stage10_execution_pending[script_filename]
            logger.debug(f"[stage10.render] Stage 10: Removed {script_filename} from pending list")

            # Execute the script with error boundary
            try:
                _execute_generated_script(
                    state,
                    execution_data['script_filename'],
                    execution_data['script_content'],
                    execution_data['target_test_case']
                )
            except Exception as exec_error:
                logger.debug(f"[stage10.render] Stage 10: Execution error for {script_filename}: {exec_error}")
                st.error(f"❌ **Execution Error**: Failed to execute {script_filename}: {exec_error}")

            # Only execute one script at a time
            break

    except Exception as e:
        logger.debug(f"[stage10.render] Stage 10: Error in _check_and_execute_pending_scripts: {e}")
        st.error(f"❌ **System Error**: {e}")


def _validate_execution_state(state) -> bool:
    """
    Validate that the system is in a proper state for script execution.

    Args:
        state: StateManager instance

    Returns:
        bool: True if execution can proceed, False otherwise
    """
    try:
        # Check if script storage is initialized
        if not hasattr(state, '_script_storage') or state._script_storage is None:
            logger.debug("[stage10.render] Stage 10: Script storage not initialized")
            return False

        # Check if there are any currently running executions
        if hasattr(state, 'stage10_execution_results'):
            # Look for any executions that might still be in progress
            for result in state.stage10_execution_results.values():
                if result.get('in_progress', False):
                    logger.debug("[stage10.render] Stage 10: Another execution is already in progress")
                    return False

        return True

    except Exception as e:
        logger.debug(f"[stage10.render] Stage 10: Error validating execution state: {e}")
        return False


def _validate_execution_data(execution_data: dict) -> bool:
    """
    Validate execution data before attempting to execute a script.

    Args:
        execution_data: Dictionary containing execution information

    Returns:
        bool: True if data is valid, False otherwise
    """
    try:
        required_keys = ['script_filename', 'script_content', 'target_test_case']

        # Check required keys exist
        for key in required_keys:
            if key not in execution_data:
                logger.debug(f"[stage10.render] Stage 10: Missing required execution data key: {key}")
                return False

        # Validate script content is not empty
        if not execution_data['script_content'] or not execution_data['script_content'].strip():
            logger.debug("[stage10.render] Stage 10: Script content is empty")
            return False

        # Validate filename is reasonable
        filename = execution_data['script_filename']
        if not filename or not filename.endswith('.py'):
            logger.debug(f"[stage10.render] Stage 10: Invalid script filename: {filename}")
            return False

        return True

    except Exception as e:
        logger.debug(f"[stage10.render] Stage 10: Error validating execution data: {e}")
        return False


def _execute_generated_script(state, script_filename, script_content, target_test_case):
    """
    Execute the generated script using pytest with the same methodology as Stage 7.

    Args:
        state (StateManager): The application state manager instance
        script_filename (str): Name of the script file
        script_content (str): Content of the generated script
        target_test_case (dict): Target test case information
    """
    import os
    import subprocess
    from datetime import datetime
    from pathlib import Path

    logger.debug("[stage10.render] Stage 10: Starting script execution")
    execution_start_time = datetime.now()

    # Mark execution as in progress
    if not hasattr(state, 'stage10_execution_results'):
        state.stage10_execution_results = {}

    # Create initial execution result with in_progress flag
    state.stage10_execution_results[script_filename] = {
        'script_filename': script_filename,
        'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
        'execution_timestamp': datetime.now().isoformat(),
        'in_progress': True,
        'success': False
    }

    try:
        # Ensure the script file exists and is written correctly
        script_path = Path(script_filename)

        # Always write the script content to ensure it's up to date
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            logger.debug(f"[stage10.render] Stage 10: Script file written successfully: {script_path}")

            # Verify the file was written correctly
            if not script_path.exists():
                raise FileNotFoundError(f"Script file was not created: {script_path}")

            # Check file size
            file_size = script_path.stat().st_size
            if file_size == 0:
                raise ValueError(f"Script file is empty: {script_path}")

            logger.debug(f"[stage10.render] Stage 10: Script file verified - size: {file_size} bytes")

        except Exception as file_error:
            error_msg = f"Failed to write script file: {file_error}"
            st.error(f"❌ **File Error**: {error_msg}")
            logger.debug(f"[stage10.render] Stage 10 file error: {file_error}")

            # Store error in state
            if not hasattr(state, 'stage10_execution_results'):
                state.stage10_execution_results = {}

            state.stage10_execution_results[script_filename] = {
                'script_filename': script_filename,
                'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
                'execution_timestamp': datetime.now().isoformat(),
                'error': error_msg,
                'success': False,
                'error_type': 'file_creation_error',
                'in_progress': False  # Mark as completed (with error)
            }
            return

        with st.spinner(f"Running generated script: {script_filename}..."):
            # Import the JUnit parser (reusing Stage 7 patterns)
            from core.junit_parser import parse_junit_xml, format_test_results_for_display

            # Set environment variables for the test run (same as Stage 7)
            env = os.environ.copy()
            env["HEADLESS"] = "0"  # Always run in visible mode
            env["PYTEST_QUIET_MODE"] = "1"  # Quiet mode for Stage 10

            # Generate timestamped result file name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_xml_path = f"stage10_results_{timestamp}.xml"

            # Quiet pytest command for Stage 10 (minimal console output)
            pytest_command = [
                "pytest",
                str(script_path),
                f"--junitxml={result_xml_path}",
                "--log-cli-level=WARNING",  # Only show warnings and errors
                "--capture=no",  # Capture output for cleaner console
                "--tb=short",  # Short traceback format
                "-q"  # Quiet mode
            ]

            logger.debug(f"[stage10.render] Stage 10: Executing pytest command: {' '.join(pytest_command)}")
            logger.debug(f"[stage10.render] Stage 10: Working directory: {os.getcwd()}")
            logger.debug(f"[stage10.render] Stage 10: Environment HEADLESS: {env.get('HEADLESS', 'not set')}")

            # Run the test script using enhanced pytest configuration
            result = subprocess.run(
                pytest_command,
                capture_output=True, text=True,
                env=env,
                cwd=os.getcwd()  # Ensure we're in the correct directory
            )

            # Store execution results in state for display
            execution_results = {
                'script_filename': script_filename,
                'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
                'execution_timestamp': datetime.now().isoformat(),
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'xml_file': result_xml_path,
                'success': result.returncode == 0,
                'pytest_command': ' '.join(pytest_command),
                'in_progress': False  # Mark as completed
            }

            # Parse XML results if available with comprehensive artifact extraction
            xml_results = None
            performance_metrics = {}
            artifacts = {}
            formatted_results = {}

            if Path(result_xml_path).exists():
                try:
                    xml_results = parse_junit_xml(result_xml_path)
                    if xml_results:
                        execution_results['xml_results'] = xml_results
                        formatted_results = format_test_results_for_display(xml_results)
                        execution_results['formatted_results'] = formatted_results
                        performance_metrics = formatted_results.get("performance_summary", {})
                        execution_results['performance_metrics'] = performance_metrics

                        # Extract artifacts from test details (screenshots, logs, etc.)
                        for test_detail in formatted_results.get("test_details", []):
                            test_artifacts = test_detail.get("artifacts", {})
                            if test_artifacts:
                                artifacts.update(test_artifacts)

                        logger.debug(f"[stage10.render] Stage 10: Successfully parsed XML results from {result_xml_path}")
                        logger.debug(f"[stage10.render] Stage 10: Extracted {len(artifacts)} artifacts from XML results")
                    else:
                        logger.debug(f"[stage10.render] Stage 10: XML file exists but parsing returned None: {result_xml_path}")
                except Exception as xml_error:
                    logger.debug(f"[stage10.render] Stage 10: Failed to parse XML results: {xml_error}")
                    execution_results['xml_parse_error'] = str(xml_error)
            else:
                logger.debug(f"[stage10.render] Stage 10: XML results file not found: {result_xml_path}")

            # Check for screenshots in the screenshots directory (following Stage 7 pattern)
            screenshots_dir = Path("screenshots")
            if screenshots_dir.exists():
                screenshot_files = list(screenshots_dir.glob("*.png"))
                if screenshot_files:
                    # Get the most recent screenshot
                    latest_screenshot = max(screenshot_files, key=os.path.getmtime)

                    # Check if it was created recently (within the last minute)
                    import time
                    if time.time() - os.path.getmtime(latest_screenshot) < 60:
                        artifacts['screenshot'] = str(latest_screenshot)
                        logger.debug(f"[stage10.render] Stage 10: Found recent screenshot: {latest_screenshot}")

            # Check for log files in the logs directory
            logs_dir = Path("logs")
            if logs_dir.exists():
                log_files = list(logs_dir.glob("*.log"))
                if log_files:
                    # Get the most recent log file
                    latest_log = max(log_files, key=os.path.getmtime)

                    # Check if it was created recently (within the last minute)
                    if time.time() - os.path.getmtime(latest_log) < 60:
                        artifacts['log'] = str(latest_log)
                        logger.debug(f"[stage10.render] Stage 10: Found recent log file: {latest_log}")

            # Store artifacts in execution results
            execution_results['artifacts'] = artifacts
            logger.debug(f"[stage10.render] Stage 10: Total artifacts collected: {len(artifacts)}")

            # Store results in state (using a Stage 10 specific field)
            if not hasattr(state, 'stage10_execution_results'):
                state.stage10_execution_results = {}

            state.stage10_execution_results[script_filename] = execution_results

            # Calculate execution time for performance monitoring
            execution_time = (datetime.now() - execution_start_time).total_seconds()
            logger.debug(f"[stage10.render] Stage 10: Script execution completed with return code {result.returncode} in {execution_time:.2f} seconds")

            # Add execution time to results
            execution_results['execution_time_seconds'] = execution_time

            # Update session state without triggering rerun (results will be displayed by main UI)
            st.session_state['state'] = state

            # Show brief immediate feedback only
            if result.returncode == 0:
                st.success("✅ **Execution completed successfully!**")
            else:
                st.error("❌ **Execution failed!**")

    except Exception as e:
        import traceback
        error_msg = f"Failed to execute script: {e}"
        traceback_str = traceback.format_exc()

        st.error(f"❌ **Execution Error**: {error_msg}")
        logger.debug(f"[stage10.render] Stage 10 execution error: {e}")
        logger.debug(f"[stage10.render] Stage 10 execution traceback: {traceback_str}")

        # Store error in state
        if not hasattr(state, 'stage10_execution_results'):
            state.stage10_execution_results = {}

        state.stage10_execution_results[script_filename] = {
            'script_filename': script_filename,
            'target_test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
            'execution_timestamp': datetime.now().isoformat(),
            'error': error_msg,
            'traceback': traceback_str,
            'success': False,
            'error_type': 'python_exception',
            'in_progress': False  # Mark as completed (with error)
        }

        # Update session state without triggering rerun (results will be displayed by main UI)
        st.session_state['state'] = state


def _display_stage10_execution_results_compact(state):
    """
    Display execution results in a compact format for Stage 10.

    Args:
        state (StateManager): The application state manager instance
    """
    if not hasattr(state, 'stage10_execution_results') or not state.stage10_execution_results:
        return

    # Get the most recent execution result
    latest_result = None
    latest_timestamp = None

    for result in state.stage10_execution_results.values():
        timestamp = result.get('execution_timestamp')
        if timestamp and (latest_timestamp is None or timestamp > latest_timestamp):
            latest_result = result
            latest_timestamp = timestamp

    if not latest_result:
        return

    # Comprehensive single-line status display
    success = latest_result.get('success', False)
    test_case_id = latest_result.get('target_test_case_id', 'Unknown')
    exec_duration = latest_result.get('execution_time_seconds')

    if success:
        # Get test results if available
        xml_results = latest_result.get('xml_results', {})
        if xml_results and 'summary' in xml_results:
            summary = xml_results['summary']
            total_tests = summary.get('total_tests', 0)
            passed_tests = summary.get('passed_tests', 0)
            duration_text = f"{exec_duration:.2f}s" if exec_duration else "unknown time"
            st.success(f"✅ **{test_case_id}** executed successfully: {passed_tests}/{total_tests} tests passed in {duration_text}")
        else:
            duration_text = f"{exec_duration:.2f}s" if exec_duration else "unknown time"
            st.success(f"✅ **{test_case_id}** executed successfully in {duration_text}")
    else:
        # Show comprehensive error information
        error_msg = latest_result.get('error', 'Unknown error')
        duration_text = f" (failed after {exec_duration:.2f}s)" if exec_duration else ""
        st.error(f"❌ **{test_case_id}** execution failed{duration_text}")

        # Show brief error details
        if len(error_msg) > 100:
            error_msg = error_msg[:97] + "..."
        st.caption(f"Error: {error_msg}")

    # Optional detailed view and clear button
    col1, col2 = st.columns([3, 1])

    with col1:
        with st.expander("📊 Execution Details", expanded=False):
            _display_stage10_execution_results_detailed(latest_result)

    with col2:
        if st.button("🗑️ Clear Results", use_container_width=True, key="stage10_clear_execution_results"):
            if hasattr(state, 'stage10_execution_results'):
                state.stage10_execution_results.clear()
                st.session_state['state'] = state
                logger.debug("[stage10.render] Stage 10: Clear results button clicked, triggering rerun")
                st.rerun()
                return


def _display_stage10_execution_results_detailed(latest_result):
    """
    Display detailed execution results in an expander.

    Args:
        latest_result: The latest execution result dictionary
    """
    # Basic execution info
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Script", latest_result.get('script_filename', 'Unknown'))

    with col2:
        st.metric("Test Case", latest_result.get('target_test_case_id', 'Unknown'))

    with col3:
        execution_time = latest_result.get('execution_timestamp', '')
        formatted_time = _format_timestamp_safe(execution_time)

        # Show execution time if available
        exec_duration = latest_result.get('execution_time_seconds')
        if exec_duration:
            st.metric("Executed At", formatted_time, f"{exec_duration:.2f}s")
        else:
            st.metric("Executed At", formatted_time)

    # Show detailed test results if available
    xml_results = latest_result.get('xml_results', {})
    if xml_results and 'summary' in xml_results:
        st.markdown("**Test Results Summary:**")
        summary = xml_results['summary']

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Tests", summary.get('total_tests', 0))
        with col2:
            st.metric("Passed", summary.get('passed_tests', 0))
        with col3:
            st.metric("Failed", summary.get('failed_tests', 0))
        with col4:
            st.metric("Duration", f"{summary.get('total_time', 0):.2f}s")

        # Show individual test details if available
        formatted_results = latest_result.get('formatted_results', {})
        test_details = formatted_results.get('test_details', [])
        if test_details:
            st.markdown("**Individual Test Results:**")
            for test in test_details:
                status_icon = "✅" if test['status'] == 'passed' else "❌"
                st.markdown(f"{status_icon} **{test['name']}** - {test['duration']}")

                if test['status'] != 'passed' and test.get('message'):
                    st.error(f"Error: {test['message']}")

    # Show performance metrics if available
    performance_metrics = latest_result.get('performance_metrics', {})
    if performance_metrics and 'aggregated' in performance_metrics:
        st.markdown("**Performance Metrics:**")
        perf_cols = st.columns(4)
        metrics_data = performance_metrics['aggregated']

        if 'execution_time' in metrics_data:
            with perf_cols[0]:
                st.metric("Execution Time", f"{metrics_data['execution_time']['average']:.3f}s")
        if 'memory_usage' in metrics_data:
            with perf_cols[1]:
                st.metric("Memory Usage", f"{metrics_data['memory_usage']['average']:.1f}MB")
        if 'cpu_usage' in metrics_data:
            with perf_cols[2]:
                st.metric("CPU Usage", f"{metrics_data['cpu_usage']['average']:.1f}%")
        if 'network_requests' in metrics_data:
            with perf_cols[3]:
                st.metric("Network Requests", int(metrics_data['network_requests']['total']))

    # Show artifacts section (screenshots, logs, etc.) - following Stage 7 pattern
    artifacts = latest_result.get('artifacts', {})
    if artifacts:
        st.markdown("**Test Artifacts:**")
        for artifact_type, artifact_path in artifacts.items():
            if os.path.exists(artifact_path):
                if artifact_type == "screenshot":
                    st.image(artifact_path, caption=f"Screenshot: {os.path.basename(artifact_path)}")
                elif artifact_type == "log":
                    # Display log file info
                    st.info(f"📄 Log File: {os.path.basename(artifact_path)}")
                    try:
                        with open(artifact_path, 'r', encoding='utf-8') as f:
                            log_content = f.read()
                        # Show first few lines as preview
                        log_lines = log_content.split('\n')
                        if len(log_lines) > 10:
                            preview = '\n'.join(log_lines[:10]) + f"\n... ({len(log_lines) - 10} more lines)"
                            st.code(preview, language="text")
                            st.caption(f"Full log available at: {artifact_path}")
                        else:
                            st.code(log_content, language="text")
                    except Exception as e:
                        st.error(f"Could not read log file: {e}")
                else:
                    st.info(f"{artifact_type.title()}: {artifact_path}")
            else:
                st.warning(f"{artifact_type.title()} file not found: {artifact_path}")

    # Show console output if available
    stdout = latest_result.get('stdout')
    stderr = latest_result.get('stderr')

    if stdout:
        st.markdown("**Console Output:**")
        st.code(stdout, language="text")

    if stderr:
        st.markdown("**Error Output:**")
        st.code(stderr, language="text")

    # Show error details if execution failed
    if not latest_result.get('success', False):
        error_msg = latest_result.get('error', 'Unknown error')
        st.markdown("**Error Details:**")
        st.error(error_msg)

        # Show traceback if available
        traceback_info = latest_result.get('traceback')
        if traceback_info:
            st.markdown("**Full Traceback:**")
            st.code(traceback_info, language='text')


def _display_selection_previews(selected_template, selected_test_case):
    """
    Display informational preview sections for both the selected template script
    and the selected target test case in a two-column layout.

    Args:
        selected_template: The selected template script dictionary
        selected_test_case: The selected target test case dictionary
    """
    # Two-column layout for comparative context
    st.markdown("---")

    # Create two columns for template and test case information
    col1, col2 = st.columns(2)

    # Left Column: Template Information
    with col1:
        st.markdown("**📋 Template Information**")
        template_info = _extract_template_preview_info(selected_template)

        st.markdown(f"**Template Name:** {template_info['template_name']}")
        st.markdown(f"**Original Test Case Objective:** {template_info['original_test_case_objective']}")

        # Additional template metadata
        st.markdown(f"**Status:** {template_info['optimization_status']}")
        st.markdown(f"**Created:** {template_info['creation_time']}")

    # Right Column: Target Test Case Information
    with col2:
        st.markdown("**🎯 Target Test Case Information**")
        test_case_info = _extract_test_case_preview_info(selected_test_case)

        st.markdown(f"**Test Case Name:** {test_case_info['test_case_id']}")
        st.markdown(f"**Test Case Objective:** {test_case_info['test_objective']}")

        # Additional test case metadata
        st.markdown(f"**Test Scenario:** {test_case_info['test_scenario']}")
        st.markdown(f"**Steps Count:** {test_case_info['steps_count']}")


def _extract_template_preview_info(template_script):
    """
    Extract preview information from template script for display.

    Args:
        template_script: Template script dictionary

    Returns:
        Dictionary with formatted preview information
    """
    try:
        # Basic template information
        template_name = template_script.get('test_case_id', 'Unknown Template')
        original_test_case_id = template_script.get('test_case_id', 'Unknown')
        optimization_status = template_script.get('optimization_status', 'Unknown')

        # Format timestamp
        timestamp = template_script.get('timestamp', datetime.now())
        if isinstance(timestamp, str):
            try:
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except:
                timestamp = datetime.now()
        creation_time = timestamp.strftime('%Y-%m-%d %H:%M')

        # Retrieve original test case objective from template metadata or state
        original_test_case_objective = _get_original_test_case_objective(template_script, original_test_case_id)

        return {
            'template_name': template_name,
            'original_test_case_id': original_test_case_id,
            'optimization_status': optimization_status.replace('_', ' ').title(),
            'creation_time': creation_time,
            'original_test_case_objective': original_test_case_objective
        }

    except Exception as e:
        logger.debug(f"[stage10.render] Error extracting template preview info: {e}")
        return {
            'template_name': 'Unknown Template',
            'original_test_case_id': 'Unknown',
            'optimization_status': 'Unknown',
            'creation_time': 'Unknown',
            'original_test_case_objective': 'Original objective not available'
        }


def _get_original_test_case_objective(template_script: Dict[str, Any], original_test_case_id: str) -> str:
    """
    Retrieve the original test case objective from template script metadata or current state.

    Args:
        template_script: The template script dictionary
        original_test_case_id: The original test case ID the template was based on

    Returns:
        The original test case objective or a fallback message
    """
    try:
        # First, try to get the objective from template metadata
        metadata = template_script.get('metadata', {})

        # Check if the original test case objective is stored in metadata
        if 'test_case_objective' in metadata:
            objective = metadata['test_case_objective']
            if objective and objective.strip():
                logger.debug(f"[stage10.render] Found original test case objective in template metadata: {objective[:50]}...")
                return objective

        # Check for alternative metadata field names
        if 'original_test_case_objective' in metadata:
            objective = metadata['original_test_case_objective']
            if objective and objective.strip():
                logger.debug(f"[stage10.render] Found original test case objective in alternative metadata field: {objective[:50]}...")
                return objective

        # Try to get from current state if available (for recently created templates)
        try:
            import streamlit as st
            if hasattr(st.session_state, 'state') and st.session_state.state:
                state = st.session_state.state

                # Check if we have test cases loaded and can find the matching one
                if hasattr(state, 'test_cases') and state.test_cases:
                    for test_case in state.test_cases:
                        if test_case.get('Test Case ID') == original_test_case_id:
                            objective = test_case.get('Test Case Objective', '')
                            if objective and objective.strip():
                                logger.debug(f"[stage10.render] Found original test case objective in current state: {objective[:50]}...")
                                return objective

                # Check if this is the currently selected test case
                if (hasattr(state, 'selected_test_case') and state.selected_test_case and
                    state.selected_test_case.get('Test Case ID') == original_test_case_id):
                    objective = state.selected_test_case.get('Test Case Objective', '')
                    if objective and objective.strip():
                        logger.debug(f"[stage10.render] Found original test case objective in selected test case: {objective[:50]}...")
                        return objective
        except Exception as state_error:
            logger.debug(f"[stage10.render] Error accessing state for original test case objective: {state_error}")

        # If we can't find the original objective, return a descriptive fallback
        logger.debug(f"[stage10.render] Could not find original test case objective for {original_test_case_id}")
        return "Original objective not available"

    except Exception as e:
        logger.debug(f"[stage10.render] Error retrieving original test case objective: {e}")
        return "Original objective not available"


def _extract_test_case_preview_info(test_case):
    """
    Extract preview information from test case for display.

    Args:
        test_case: Test case dictionary

    Returns:
        Dictionary with formatted preview information
    """
    try:
        # Basic test case information
        test_case_id = test_case.get('Test Case ID', 'Unknown')
        test_objective = test_case.get('Test Case Objective', 'No objective specified')

        # Get steps information
        steps = test_case.get('Steps', [])
        steps_count = len(steps)

        # Create test scenario description
        test_scenario = test_case.get('Test Scenario', '')
        if not test_scenario:
            # Generate scenario from objective
            if 'login' in test_objective.lower():
                test_scenario = 'User Authentication'
            elif 'search' in test_objective.lower():
                test_scenario = 'Search Functionality'
            elif 'form' in test_objective.lower():
                test_scenario = 'Form Interaction'
            else:
                test_scenario = 'UI Interaction'

        return {
            'test_case_id': test_case_id,
            'test_objective': test_objective[:100] + "..." if len(test_objective) > 100 else test_objective,
            'steps_count': steps_count,
            'test_scenario': test_scenario
        }

    except Exception as e:
        logger.debug(f"[stage10.render] Error extracting test case preview info: {e}")
        return {
            'test_case_id': 'Unknown',
            'test_objective': 'Test case information unavailable',
            'steps_count': 0,
            'test_scenario': 'Unknown'
        }


def _format_timestamp_safe(timestamp_str: str) -> str:
    """Safely format timestamp string with fallback."""
    try:
        if timestamp_str:
            dt = datetime.fromisoformat(timestamp_str)
            return dt.strftime('%H:%M:%S')
        return 'Unknown'
    except Exception:
        return 'Unknown'


def _create_safe_filename(target_test_case_id: str, formatted_time: str) -> str:
    """Create a safe filename with proper sanitization."""
    safe_id = target_test_case_id.replace(' ', '_').replace('/', '_')
    safe_time = formatted_time.replace(':', '')
    return f"template_generated_{safe_id}_{safe_time}.py"

