#!/usr/bin/env python3
"""
Interactive Element Selector API Client

This module provides a client interface for communicating with the standalone
Interactive Element Selector API service from within the GretahAI ScriptWeaver
Streamlit application.

Key Features:
- Asynchronous API communication
- Session management and polling
- Error handling and retry logic
- Integration with Streamlit session state
- Automatic cleanup and timeout handling

© 2025 Cogniron All Rights Reserved.
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Any, Tuple
from datetime import datetime, timedelta

import requests
import streamlit as st

# Configure logging
logger = logging.getLogger(__name__)

# Default API configuration
DEFAULT_API_HOST = "127.0.0.1"
DEFAULT_API_PORT = "8502"
DEFAULT_TIMEOUT = 300  # 5 minutes
POLL_INTERVAL = 2  # seconds


class SelectorAPIClient:
    """Client for communicating with the Interactive Element Selector API"""
    
    def __init__(self, host: str = None, port: str = None, timeout: int = DEFAULT_TIMEOUT):
        """
        Initialize the API client.
        
        Args:
            host: API service host (default: 127.0.0.1)
            port: API service port (default: 8502)
            timeout: Request timeout in seconds (default: 300)
        """
        self.host = host or DEFAULT_API_HOST
        self.port = port or DEFAULT_API_PORT
        self.timeout = timeout
        self.base_url = f"http://{self.host}:{self.port}"
        
        logger.info(f"Initialized SelectorAPIClient for {self.base_url}")
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an HTTP request to the API service.
        
        Args:
            method: HTTP method (GET, POST, DELETE)
            endpoint: API endpoint path
            **kwargs: Additional arguments for requests
            
        Returns:
            requests.Response object
            
        Raises:
            requests.RequestException: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                timeout=10,  # Short timeout for API calls
                **kwargs
            )
            response.raise_for_status()
            return response
        
        except requests.exceptions.ConnectionError:
            raise ConnectionError(f"Could not connect to Interactive Selector API at {self.base_url}. "
                                f"Please ensure the API service is running.")
        except requests.exceptions.Timeout:
            raise TimeoutError(f"Request to {url} timed out")
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"API request failed: {e}")
    
    def health_check(self) -> bool:
        """
        Check if the API service is running and healthy.
        
        Returns:
            bool: True if service is healthy, False otherwise
        """
        try:
            response = self._make_request("GET", "/")
            data = response.json()
            return data.get("status") == "running"
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False
    
    def start_selector_session(self, url: str, description: str, gap_id: str = None) -> Tuple[bool, str, str]:
        """
        Start a new interactive element selector session.
        
        Args:
            url: Target website URL
            description: Description of element to select
            gap_id: Optional gap ID for tracking
            
        Returns:
            Tuple of (success: bool, session_id: str, message: str)
        """
        try:
            payload = {
                "url": url,
                "description": description
            }
            if gap_id:
                payload["gap_id"] = gap_id
            
            response = self._make_request("POST", "/selector/start", json=payload)
            data = response.json()
            
            session_id = data.get("session_id")
            message = data.get("message", "Session started successfully")
            
            logger.info(f"Started selector session {session_id} for URL: {url}")
            return True, session_id, message
        
        except Exception as e:
            error_msg = f"Failed to start selector session: {e}"
            logger.error(error_msg)
            return False, "", error_msg
    
    def get_session_status(self, session_id: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        Get the status and results of a selector session.
        
        Args:
            session_id: Session ID to check
            
        Returns:
            Tuple of (success: bool, session_data: dict, error_message: str)
        """
        try:
            response = self._make_request("GET", f"/selector/status/{session_id}")
            data = response.json()
            
            return True, data, ""
        
        except Exception as e:
            error_msg = f"Failed to get session status: {e}"
            logger.error(error_msg)
            return False, {}, error_msg
    
    def wait_for_completion(self, session_id: str, timeout: int = None) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        Wait for a selector session to complete and return the result.
        
        Args:
            session_id: Session ID to wait for
            timeout: Maximum time to wait in seconds (default: self.timeout)
            
        Returns:
            Tuple of (success: bool, result: dict or None, message: str)
        """
        timeout = timeout or self.timeout
        start_time = time.time()
        
        logger.info(f"Waiting for session {session_id} to complete (timeout: {timeout}s)")
        
        while time.time() - start_time < timeout:
            try:
                success, session_data, error = self.get_session_status(session_id)
                
                if not success:
                    return False, None, error
                
                status = session_data.get("status")
                
                if status == "completed":
                    result = session_data.get("result")
                    logger.info(f"Session {session_id} completed successfully")
                    return True, result, "Element selection completed successfully"
                
                elif status == "failed":
                    error_msg = session_data.get("error_message", "Unknown error")
                    logger.error(f"Session {session_id} failed: {error_msg}")
                    return False, None, f"Element selection failed: {error_msg}"
                
                elif status in ["pending", "in_progress"]:
                    # Still running, continue waiting
                    time.sleep(POLL_INTERVAL)
                    continue
                
                else:
                    logger.warning(f"Unknown session status: {status}")
                    return False, None, f"Unknown session status: {status}"
            
            except Exception as e:
                logger.error(f"Error while waiting for session {session_id}: {e}")
                return False, None, f"Error checking session status: {e}"
        
        # Timeout reached
        logger.warning(f"Session {session_id} timed out after {timeout} seconds")
        return False, None, f"Element selection timed out after {timeout} seconds"
    
    def delete_session(self, session_id: str) -> bool:
        """
        Delete a selector session and clean up resources.
        
        Args:
            session_id: Session ID to delete
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            self._make_request("DELETE", f"/selector/session/{session_id}")
            logger.info(f"Deleted session {session_id}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False
    
    def list_active_sessions(self) -> Tuple[bool, list, str]:
        """
        List all active selector sessions.
        
        Returns:
            Tuple of (success: bool, sessions: list, error_message: str)
        """
        try:
            response = self._make_request("GET", "/selector/sessions")
            data = response.json()
            
            sessions = data.get("sessions", [])
            return True, sessions, ""
        
        except Exception as e:
            error_msg = f"Failed to list sessions: {e}"
            logger.error(error_msg)
            return False, [], error_msg


def select_element_interactively_api(url: str, description: str = "Element locator", 
                                   gap_id: str = None, timeout: int = DEFAULT_TIMEOUT) -> Optional[Dict[str, Any]]:
    """
    High-level function to perform interactive element selection using the API service.
    
    This function provides a simple interface that matches the original interactive selector
    but uses the standalone API service for isolation.
    
    Args:
        url: Target website URL
        description: Description of element to select
        gap_id: Optional gap ID for tracking
        timeout: Maximum time to wait for selection
        
    Returns:
        Dict containing selected element information, or None if failed
    """
    client = SelectorAPIClient(timeout=timeout)
    
    # Check if API service is available
    if not client.health_check():
        st.error("❌ Interactive Selector API service is not available. Please ensure it's running.")
        logger.error("Interactive Selector API service health check failed")
        return None
    
    try:
        # Start the selector session
        success, session_id, message = client.start_selector_session(url, description, gap_id)
        
        if not success:
            st.error(f"❌ Failed to start element selection: {message}")
            return None
        
        # Show progress to user
        with st.spinner(f"🎯 {message}"):
            st.info("A browser window will open for element selection. Please select an element and the page will update automatically.")
            
            # Wait for completion
            success, result, message = client.wait_for_completion(session_id, timeout)
            
            # Clean up session
            client.delete_session(session_id)
            
            if success and result:
                st.success(f"✅ Element selected successfully!")
                return result
            else:
                st.warning(f"⚠️ {message}")
                return None
    
    except Exception as e:
        logger.error(f"Error in select_element_interactively_api: {e}")
        st.error(f"❌ Unexpected error during element selection: {e}")
        return None


# Streamlit session state helpers for API-based selector
def store_api_selector_result(gap_id: str, result: Dict[str, Any]):
    """Store API selector result in Streamlit session state"""
    if result:
        # Extract the best locator from the result
        from core.gap_analysis import _extract_best_locator
        locator = _extract_best_locator(result)
        
        if locator:
            interactive_key = f"interactive_locator_{gap_id}"
            st.session_state[interactive_key] = locator
            logger.info(f"Stored API selector result for gap {gap_id}: {locator}")
            return True
    
    return False


def clear_api_selector_flags(gap_id: str = None):
    """Clear API selector related flags from session state"""
    keys_to_remove = []
    
    if gap_id:
        # Clear specific gap flags
        keys_to_remove.extend([
            f"interactive_locator_{gap_id}",
            f"interactive_selector_pending_{gap_id}",
            f"interactive_selector_in_progress_{gap_id}"
        ])
    else:
        # Clear all interactive selector flags
        for key in st.session_state.keys():
            if any(key.startswith(prefix) for prefix in [
                'interactive_locator_',
                'interactive_selector_pending_',
                'interactive_selector_in_progress_',
                'interactive_selector_polling_'
            ]):
                keys_to_remove.append(key)
    
    for key in keys_to_remove:
        if key in st.session_state:
            del st.session_state[key]
            logger.debug(f"Cleared session state key: {key}")
