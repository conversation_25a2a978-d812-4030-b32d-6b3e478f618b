#!/usr/bin/env python3
"""
Interactive Element Selector API Service

This standalone FastAPI service provides isolated interactive element selector functionality
for GretahAI ScriptWeaver Stage 10. It runs independently from the main Streamlit application
to prevent interference and state conflicts.

Key Features:
- Asynchronous element selection sessions
- Process isolation from main Streamlit app
- RESTful API for communication
- Session-based result storage
- Automatic cleanup of expired sessions

© 2025 Cogniron All Rights Reserved.
"""

import asyncio
import logging
import os
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Session storage for selector results
selector_sessions: Dict[str, Dict[str, Any]] = {}
SESSION_TIMEOUT_MINUTES = 30


@dataclass
class SelectorSession:
    """Data class for tracking selector sessions"""
    session_id: str
    url: str
    description: str
    status: str  # 'pending', 'in_progress', 'completed', 'failed', 'expired'
    created_at: datetime
    updated_at: datetime
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class SelectorRequest(BaseModel):
    """Request model for initiating element selection"""
    url: str = Field(..., description="Target website URL for element selection")
    description: str = Field(..., description="Description of the element to select")
    gap_id: Optional[str] = Field(None, description="Optional gap ID for tracking")


class SelectorResponse(BaseModel):
    """Response model for selector operations"""
    session_id: str
    status: str
    message: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class SessionStatusResponse(BaseModel):
    """Response model for session status queries"""
    session_id: str
    status: str
    url: str
    description: str
    created_at: str
    updated_at: str
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


def cleanup_expired_sessions():
    """Remove expired sessions from storage"""
    current_time = datetime.now()
    expired_sessions = []
    
    for session_id, session_data in selector_sessions.items():
        session_age = current_time - session_data['created_at']
        if session_age > timedelta(minutes=SESSION_TIMEOUT_MINUTES):
            expired_sessions.append(session_id)
    
    for session_id in expired_sessions:
        logger.info(f"Cleaning up expired session: {session_id}")
        del selector_sessions[session_id]
    
    return len(expired_sessions)


async def run_interactive_selector(session_id: str, url: str, description: str):
    """
    Run the interactive element selector in a separate process.
    
    This function launches the browser automation and updates the session
    with the results when complete.
    """
    try:
        logger.info(f"Starting interactive selector for session {session_id}")
        
        # Update session status
        if session_id in selector_sessions:
            selector_sessions[session_id]['status'] = 'in_progress'
            selector_sessions[session_id]['updated_at'] = datetime.now()
        
        # Import the interactive selector (isolated from main app)
        from core.interactive_selector import launch_interactive_selector
        
        # Launch the selector with timeout
        result = await asyncio.wait_for(
            asyncio.to_thread(launch_interactive_selector, url),
            timeout=300  # 5 minute timeout
        )
        
        # Update session with results
        if session_id in selector_sessions:
            if result:
                selector_sessions[session_id]['status'] = 'completed'
                selector_sessions[session_id]['result'] = result
                logger.info(f"Interactive selector completed successfully for session {session_id}")
            else:
                selector_sessions[session_id]['status'] = 'failed'
                selector_sessions[session_id]['error_message'] = "No element was selected or selection timed out"
                logger.warning(f"Interactive selector failed for session {session_id}: No element selected")
            
            selector_sessions[session_id]['updated_at'] = datetime.now()
    
    except asyncio.TimeoutError:
        logger.error(f"Interactive selector timed out for session {session_id}")
        if session_id in selector_sessions:
            selector_sessions[session_id]['status'] = 'failed'
            selector_sessions[session_id]['error_message'] = "Selection process timed out"
            selector_sessions[session_id]['updated_at'] = datetime.now()
    
    except Exception as e:
        logger.error(f"Error in interactive selector for session {session_id}: {e}")
        if session_id in selector_sessions:
            selector_sessions[session_id]['status'] = 'failed'
            selector_sessions[session_id]['error_message'] = str(e)
            selector_sessions[session_id]['updated_at'] = datetime.now()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Interactive Selector API Service starting up...")
    yield
    logger.info("Interactive Selector API Service shutting down...")


# Create FastAPI application
app = FastAPI(
    title="Interactive Element Selector API",
    description="Standalone API service for interactive element selection in GretahAI ScriptWeaver",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware for Streamlit integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501", "http://127.0.0.1:8501"],  # Streamlit default ports
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "Interactive Element Selector API",
        "status": "running",
        "version": "1.0.0",
        "active_sessions": len(selector_sessions),
        "timestamp": datetime.now().isoformat()
    }


@app.post("/selector/start", response_model=SelectorResponse)
async def start_selector_session(
    request: SelectorRequest,
    background_tasks: BackgroundTasks
):
    """
    Start a new interactive element selector session.
    
    This endpoint initiates a new browser-based element selection session
    and returns a session ID for tracking progress.
    """
    try:
        # Generate unique session ID
        session_id = str(uuid.uuid4())
        
        # Create session record
        session_data = {
            'session_id': session_id,
            'url': request.url,
            'description': request.description,
            'status': 'pending',
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'result': None,
            'error_message': None
        }
        
        # Store session
        selector_sessions[session_id] = session_data
        
        # Start the interactive selector in background
        background_tasks.add_task(
            run_interactive_selector,
            session_id,
            request.url,
            request.description
        )
        
        logger.info(f"Started new selector session {session_id} for URL: {request.url}")
        
        return SelectorResponse(
            session_id=session_id,
            status="pending",
            message=f"Interactive selector session started. Browser window will open shortly for: {request.description}"
        )
    
    except Exception as e:
        logger.error(f"Error starting selector session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start selector session: {str(e)}")


@app.get("/selector/status/{session_id}", response_model=SessionStatusResponse)
async def get_selector_status(session_id: str):
    """
    Get the status and results of a selector session.
    
    Returns the current status, and results if the session is complete.
    """
    if session_id not in selector_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session_data = selector_sessions[session_id]
    
    return SessionStatusResponse(
        session_id=session_id,
        status=session_data['status'],
        url=session_data['url'],
        description=session_data['description'],
        created_at=session_data['created_at'].isoformat(),
        updated_at=session_data['updated_at'].isoformat(),
        result=session_data['result'],
        error_message=session_data['error_message']
    )


@app.delete("/selector/session/{session_id}")
async def delete_selector_session(session_id: str):
    """Delete a selector session and clean up resources"""
    if session_id not in selector_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    del selector_sessions[session_id]
    logger.info(f"Deleted selector session: {session_id}")
    
    return {"message": f"Session {session_id} deleted successfully"}


@app.get("/selector/sessions")
async def list_active_sessions():
    """List all active selector sessions"""
    # Clean up expired sessions first
    cleanup_expired_sessions()
    
    sessions = []
    for session_id, session_data in selector_sessions.items():
        sessions.append({
            'session_id': session_id,
            'status': session_data['status'],
            'url': session_data['url'],
            'description': session_data['description'],
            'created_at': session_data['created_at'].isoformat(),
            'updated_at': session_data['updated_at'].isoformat()
        })
    
    return {
        "active_sessions": len(sessions),
        "sessions": sessions
    }


@app.post("/selector/cleanup")
async def cleanup_sessions():
    """Manually trigger cleanup of expired sessions"""
    cleaned_count = cleanup_expired_sessions()
    return {
        "message": f"Cleanup completed. Removed {cleaned_count} expired sessions.",
        "remaining_sessions": len(selector_sessions)
    }


if __name__ == "__main__":
    # Run the API service
    port = int(os.getenv("SELECTOR_API_PORT", "8502"))
    host = os.getenv("SELECTOR_API_HOST", "127.0.0.1")
    
    logger.info(f"Starting Interactive Selector API on {host}:{port}")
    
    uvicorn.run(
        "interactive_selector_service:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
