# Interactive Element Selector API

## Overview

The Interactive Element Selector API is a standalone service that provides isolated interactive element selection functionality for GretahAI ScriptWeaver Stage 10. This API-based approach solves interference issues that occurred when the interactive selector ran directly within the Streamlit application.

## Problem Solved

### Previous Issues
- **UI Freezing**: Interactive selector blocked Streamlit's main thread
- **Session State Conflicts**: Complex flag management caused state pollution
- **Browser Process Conflicts**: WebDriver instances interfered with Streamlit execution
- **Rerun Prevention**: Selector activity prevented normal UI updates

### API-Based Solution
- **Process Isolation**: Browser automation runs in separate process
- **Asynchronous Communication**: Non-blocking API calls from Streamlit
- **Clean State Management**: Minimal session state usage
- **Independent Lifecycle**: API service runs independently from main app

## Architecture

```
┌─────────────────────┐    HTTP API    ┌──────────────────────┐
│                     │   Requests     │                      │
│  Streamlit App      │◄──────────────►│  Selector API        │
│  (Stage 10)         │                │  Service             │
│                     │                │                      │
└─────────────────────┘                └──────────────────────┘
                                                   │
                                                   │ Launches
                                                   ▼
                                        ┌──────────────────────┐
                                        │                      │
                                        │  Browser Process     │
                                        │  (WebDriver)         │
                                        │                      │
                                        └──────────────────────┘
```

## Components

### 1. API Service (`interactive_selector_service.py`)
- **FastAPI-based service** running on separate port (default: 8502)
- **Session management** for tracking selector operations
- **Background task execution** for browser automation
- **Automatic cleanup** of expired sessions

### 2. API Client (`selector_client.py`)
- **HTTP client** for communicating with API service
- **Streamlit integration** helpers
- **Error handling** and retry logic
- **Session state management** for results

### 3. Service Launcher (`start_selector_api.py`)
- **Command-line interface** for starting the service
- **Dependency checking** and validation
- **Configuration management**
- **Graceful shutdown** handling

## Installation and Setup

### 1. Install Dependencies

```bash
# Install API service dependencies
pip install -r api/requirements.txt
```

### 2. Start the API Service

```bash
# Basic startup (default: 127.0.0.1:8502)
python api/start_selector_api.py

# Custom host and port
python api/start_selector_api.py --host 0.0.0.0 --port 8503

# Debug mode with auto-reload
python api/start_selector_api.py --debug

# Check dependencies
python api/start_selector_api.py --check-deps

# Test WebDriver setup
python api/start_selector_api.py --check-webdriver
```

### 3. Verify Service Health

```bash
# Check if service is running
curl http://127.0.0.1:8502/

# Expected response:
{
  "service": "Interactive Element Selector API",
  "status": "running",
  "version": "1.0.0",
  "active_sessions": 0,
  "timestamp": "2025-01-31T12:00:00"
}
```

## API Endpoints

### Health Check
- **GET** `/` - Service health and status

### Selector Operations
- **POST** `/selector/start` - Start new element selection session
- **GET** `/selector/status/{session_id}` - Get session status and results
- **DELETE** `/selector/session/{session_id}` - Delete session and cleanup

### Session Management
- **GET** `/selector/sessions` - List all active sessions
- **POST** `/selector/cleanup` - Manually cleanup expired sessions

## Usage in Stage 10

### Automatic Integration
The API-based selector is automatically used in Stage 10 when:
1. The API service is running and healthy
2. Gap analysis detects locator-type gaps
3. User clicks "👆 Select" buttons

### Manual Usage
```python
from api.selector_client import select_element_interactively_api

# Perform element selection
result = select_element_interactively_api(
    url="https://example.com",
    description="Login button",
    gap_id="gap_001",
    timeout=300
)

if result:
    print(f"Selected element: {result}")
```

## Configuration

### Environment Variables
- `SELECTOR_API_HOST` - API service host (default: 127.0.0.1)
- `SELECTOR_API_PORT` - API service port (default: 8502)
- `SELECTOR_API_DEBUG` - Enable debug mode (default: false)

### Service Configuration
- **Session Timeout**: 30 minutes (configurable)
- **Selection Timeout**: 5 minutes per session
- **Polling Interval**: 2 seconds for status checks
- **CORS Origins**: Streamlit ports (8501, 8502)

## Troubleshooting

### Service Won't Start
1. **Check dependencies**: `python api/start_selector_api.py --check-deps`
2. **Test WebDriver**: `python api/start_selector_api.py --check-webdriver`
3. **Port conflicts**: Use different port with `--port` option
4. **Permissions**: Ensure WebDriver has proper permissions

### Connection Issues
1. **Service health**: Check `http://127.0.0.1:8502/`
2. **Firewall**: Ensure port 8502 is accessible
3. **Network**: Verify host/port configuration
4. **Logs**: Check service logs for errors

### Selection Failures
1. **URL accessibility**: Ensure target website is reachable
2. **WebDriver issues**: Test with standalone WebDriver
3. **Timeout settings**: Increase timeout for slow sites
4. **Browser compatibility**: Check Chrome/ChromeDriver versions

## Development

### Running Tests
```bash
# Test API endpoints
python -m pytest tests/test_selector_api.py

# Test client integration
python -m pytest tests/test_selector_client.py
```

### Adding Features
1. **New endpoints**: Add to `interactive_selector_service.py`
2. **Client methods**: Update `selector_client.py`
3. **Documentation**: Update this file and API docs

## Migration from Direct Integration

### Changes Made
1. **Gap Analysis**: Updated `_handle_gap_interactive_element_selection()` to use API
2. **Stage 10**: Removed complex state management and interference checks
3. **Session State**: Simplified flag management
4. **Error Handling**: Improved with API-based approach

### Backward Compatibility
- **Existing functionality**: All Stage 10 features remain the same
- **User experience**: Identical workflow for element selection
- **Data formats**: Same locator extraction and storage
- **Integration**: Seamless with existing gap analysis

## Performance Benefits

### Reduced Interference
- **No UI blocking**: Streamlit remains responsive during selection
- **Clean state**: Minimal session state pollution
- **Faster reruns**: No complex state checks required

### Better Resource Management
- **Process isolation**: Browser crashes don't affect main app
- **Memory efficiency**: Separate process for heavy operations
- **Scalability**: Multiple sessions can run concurrently

### Improved Reliability
- **Error isolation**: API failures don't crash main app
- **Timeout handling**: Better control over long-running operations
- **Recovery**: Automatic cleanup of failed sessions

## Security Considerations

### Network Security
- **Local binding**: Default configuration binds to localhost only
- **CORS restrictions**: Limited to Streamlit origins
- **No authentication**: Suitable for local development only

### Production Deployment
- **Authentication**: Add API key or token-based auth
- **HTTPS**: Use TLS for encrypted communication
- **Network isolation**: Deploy in secure network segment
- **Rate limiting**: Implement request rate limits

## Future Enhancements

### Planned Features
- **Multiple browser support**: Firefox, Safari, Edge
- **Parallel sessions**: Multiple concurrent selections
- **Result caching**: Cache element information
- **Advanced selectors**: CSS and XPath validation

### Integration Improvements
- **Auto-discovery**: Automatic API service detection
- **Health monitoring**: Service health checks in UI
- **Configuration UI**: In-app API service configuration
- **Batch operations**: Multiple element selection

---

© 2025 Cogniron All Rights Reserved.
